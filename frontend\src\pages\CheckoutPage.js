import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useForm } from 'react-hook-form';
import { loadStripe } from '@stripe/stripe-js';
import { Elements, CardElement, useStripe, useElements } from '@stripe/react-stripe-js';
import { useCart } from '../contexts/CartContext';
import { useAuth } from '../contexts/AuthContext';
import { ordersAPI, formatPrice } from '../utils/api';
import { Lock, CreditCard, Truck, MapPin } from 'lucide-react';
import LoadingSpinner from '../components/ui/LoadingSpinner';
import toast from 'react-hot-toast';

// Initialize Stripe
const stripePromise = loadStripe(process.env.REACT_APP_STRIPE_PUBLISHABLE_KEY);

const CheckoutPage = () => {
  return (
    <Elements stripe={stripePromise}>
      <CheckoutForm />
    </Elements>
  );
};

const CheckoutForm = () => {
  const { cart, clearCart } = useCart();
  const { user } = useAuth();
  const navigate = useNavigate();
  const stripe = useStripe();
  const elements = useElements();

  const [processing, setProcessing] = useState(false);
  const [paymentIntent, setPaymentIntent] = useState(null);
  const [sameAsShipping, setSameAsShipping] = useState(true);

  const {
    register,
    handleSubmit,
    watch,
    setValue,
    formState: { errors }
  } = useForm({
    defaultValues: {
      shippingAddress: {
        firstName: user?.firstName || '',
        lastName: user?.lastName || '',
        email: user?.email || '',
        phone: user?.phone || '',
        address1: user?.address?.street || '',
        city: user?.address?.city || '',
        state: user?.address?.state || '',
        zipCode: user?.address?.zipCode || '',
        country: user?.address?.country || 'US'
      },
      billingAddress: {
        firstName: user?.firstName || '',
        lastName: user?.lastName || '',
        email: user?.email || '',
        phone: user?.phone || '',
        address1: user?.address?.street || '',
        city: user?.address?.city || '',
        state: user?.address?.state || '',
        zipCode: user?.address?.zipCode || '',
        country: user?.address?.country || 'US'
      },
      shippingMethod: 'standard'
    }
  });

  const shippingAddress = watch('shippingAddress');

  // Create payment intent when component mounts
  useEffect(() => {
    const createPaymentIntent = async () => {
      try {
        const response = await ordersAPI.createPaymentIntent();
        setPaymentIntent(response.data.data);
      } catch (error) {
        toast.error('Failed to initialize payment');
        navigate('/cart');
      }
    };

    if (cart && cart.items && cart.items.length > 0) {
      createPaymentIntent();
    } else {
      navigate('/cart');
    }
  }, [cart, navigate]);

  // Copy shipping address to billing address
  useEffect(() => {
    if (sameAsShipping) {
      Object.keys(shippingAddress).forEach(key => {
        setValue(`billingAddress.${key}`, shippingAddress[key]);
      });
    }
  }, [sameAsShipping, shippingAddress, setValue]);

  const onSubmit = async (data) => {
    if (!stripe || !elements || !paymentIntent) {
      return;
    }

    setProcessing(true);

    try {
      const cardElement = elements.getElement(CardElement);

      // Confirm payment with Stripe
      const { error, paymentIntent: confirmedPaymentIntent } = await stripe.confirmCardPayment(
        paymentIntent.clientSecret,
        {
          payment_method: {
            card: cardElement,
            billing_details: {
              name: `${data.billingAddress.firstName} ${data.billingAddress.lastName}`,
              email: data.billingAddress.email,
              phone: data.billingAddress.phone,
              address: {
                line1: data.billingAddress.address1,
                line2: data.billingAddress.address2 || '',
                city: data.billingAddress.city,
                state: data.billingAddress.state,
                postal_code: data.billingAddress.zipCode,
                country: data.billingAddress.country
              }
            }
          }
        }
      );

      if (error) {
        toast.error(error.message);
        setProcessing(false);
        return;
      }

      // Create order in backend
      const orderData = {
        paymentIntentId: confirmedPaymentIntent.id,
        shippingAddress: data.shippingAddress,
        billingAddress: data.billingAddress,
        shippingMethod: data.shippingMethod,
        customerNotes: data.customerNotes
      };

      const orderResponse = await ordersAPI.checkout(orderData);

      // Clear cart and redirect to success page
      await clearCart();
      navigate('/order-success', {
        state: { order: orderResponse.data.data.order }
      });

    } catch (error) {
      toast.error('Payment failed. Please try again.');
      setProcessing(false);
    }
  };

  if (!cart || !cart.items || cart.items.length === 0) {
    navigate('/cart');
    return null;
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container py-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">Checkout</h1>

        <form onSubmit={handleSubmit(onSubmit)}>
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Checkout Form */}
            <div className="lg:col-span-2 space-y-8">
              {/* Shipping Address */}
              <div className="bg-white rounded-lg shadow-sm p-6">
                <div className="flex items-center space-x-2 mb-6">
                  <MapPin className="h-5 w-5 text-primary-600" />
                  <h2 className="text-xl font-semibold text-gray-900">Shipping Address</h2>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="label">First Name *</label>
                    <input
                      {...register('shippingAddress.firstName', { required: 'First name is required' })}
                      className={`input ${errors.shippingAddress?.firstName ? 'input-error' : ''}`}
                    />
                    {errors.shippingAddress?.firstName && (
                      <p className="mt-1 text-sm text-red-600">{errors.shippingAddress.firstName.message}</p>
                    )}
                  </div>

                  <div>
                    <label className="label">Last Name *</label>
                    <input
                      {...register('shippingAddress.lastName', { required: 'Last name is required' })}
                      className={`input ${errors.shippingAddress?.lastName ? 'input-error' : ''}`}
                    />
                    {errors.shippingAddress?.lastName && (
                      <p className="mt-1 text-sm text-red-600">{errors.shippingAddress.lastName.message}</p>
                    )}
                  </div>

                  <div className="md:col-span-2">
                    <label className="label">Email *</label>
                    <input
                      type="email"
                      {...register('shippingAddress.email', {
                        required: 'Email is required',
                        pattern: {
                          value: /^\S+@\S+$/i,
                          message: 'Invalid email address'
                        }
                      })}
                      className={`input ${errors.shippingAddress?.email ? 'input-error' : ''}`}
                    />
                    {errors.shippingAddress?.email && (
                      <p className="mt-1 text-sm text-red-600">{errors.shippingAddress.email.message}</p>
                    )}
                  </div>

                  <div className="md:col-span-2">
                    <label className="label">Phone</label>
                    <input
                      type="tel"
                      {...register('shippingAddress.phone')}
                      className="input"
                    />
                  </div>

                  <div className="md:col-span-2">
                    <label className="label">Address *</label>
                    <input
                      {...register('shippingAddress.address1', { required: 'Address is required' })}
                      className={`input ${errors.shippingAddress?.address1 ? 'input-error' : ''}`}
                    />
                    {errors.shippingAddress?.address1 && (
                      <p className="mt-1 text-sm text-red-600">{errors.shippingAddress.address1.message}</p>
                    )}
                  </div>

                  <div className="md:col-span-2">
                    <label className="label">Apartment, suite, etc. (optional)</label>
                    <input
                      {...register('shippingAddress.address2')}
                      className="input"
                    />
                  </div>

                  <div>
                    <label className="label">City *</label>
                    <input
                      {...register('shippingAddress.city', { required: 'City is required' })}
                      className={`input ${errors.shippingAddress?.city ? 'input-error' : ''}`}
                    />
                    {errors.shippingAddress?.city && (
                      <p className="mt-1 text-sm text-red-600">{errors.shippingAddress.city.message}</p>
                    )}
                  </div>

                  <div>
                    <label className="label">State *</label>
                    <input
                      {...register('shippingAddress.state', { required: 'State is required' })}
                      className={`input ${errors.shippingAddress?.state ? 'input-error' : ''}`}
                    />
                    {errors.shippingAddress?.state && (
                      <p className="mt-1 text-sm text-red-600">{errors.shippingAddress.state.message}</p>
                    )}
                  </div>

                  <div>
                    <label className="label">ZIP Code *</label>
                    <input
                      {...register('shippingAddress.zipCode', { required: 'ZIP code is required' })}
                      className={`input ${errors.shippingAddress?.zipCode ? 'input-error' : ''}`}
                    />
                    {errors.shippingAddress?.zipCode && (
                      <p className="mt-1 text-sm text-red-600">{errors.shippingAddress.zipCode.message}</p>
                    )}
                  </div>

                  <div>
                    <label className="label">Country *</label>
                    <select
                      {...register('shippingAddress.country', { required: 'Country is required' })}
                      className={`input ${errors.shippingAddress?.country ? 'input-error' : ''}`}
                    >
                      <option value="US">United States</option>
                      <option value="CA">Canada</option>
                    </select>
                  </div>
                </div>
              </div>

              {/* Shipping Method */}
              <div className="bg-white rounded-lg shadow-sm p-6">
                <div className="flex items-center space-x-2 mb-6">
                  <Truck className="h-5 w-5 text-primary-600" />
                  <h2 className="text-xl font-semibold text-gray-900">Shipping Method</h2>
                </div>

                <div className="space-y-3">
                  <label className="flex items-center space-x-3 cursor-pointer p-3 border rounded-lg hover:bg-gray-50">
                    <input
                      type="radio"
                      value="standard"
                      {...register('shippingMethod')}
                      className="h-4 w-4 text-primary-600 focus:ring-primary-500"
                    />
                    <div className="flex-1">
                      <div className="flex items-center justify-between">
                        <span className="font-medium text-gray-900">Standard Shipping</span>
                        <span className="text-gray-600">
                          {cart.subtotal >= 100 ? 'FREE' : '$9.99'}
                        </span>
                      </div>
                      <p className="text-sm text-gray-600">5-7 business days</p>
                    </div>
                  </label>

                  <label className="flex items-center space-x-3 cursor-pointer p-3 border rounded-lg hover:bg-gray-50">
                    <input
                      type="radio"
                      value="express"
                      {...register('shippingMethod')}
                      className="h-4 w-4 text-primary-600 focus:ring-primary-500"
                    />
                    <div className="flex-1">
                      <div className="flex items-center justify-between">
                        <span className="font-medium text-gray-900">Express Shipping</span>
                        <span className="text-gray-600">$19.99</span>
                      </div>
                      <p className="text-sm text-gray-600">2-3 business days</p>
                    </div>
                  </label>

                  <label className="flex items-center space-x-3 cursor-pointer p-3 border rounded-lg hover:bg-gray-50">
                    <input
                      type="radio"
                      value="overnight"
                      {...register('shippingMethod')}
                      className="h-4 w-4 text-primary-600 focus:ring-primary-500"
                    />
                    <div className="flex-1">
                      <div className="flex items-center justify-between">
                        <span className="font-medium text-gray-900">Overnight Shipping</span>
                        <span className="text-gray-600">$39.99</span>
                      </div>
                      <p className="text-sm text-gray-600">Next business day</p>
                    </div>
                  </label>
                </div>
              </div>

              {/* Billing Address */}
              <div className="bg-white rounded-lg shadow-sm p-6">
                <div className="flex items-center justify-between mb-6">
                  <div className="flex items-center space-x-2">
                    <CreditCard className="h-5 w-5 text-primary-600" />
                    <h2 className="text-xl font-semibold text-gray-900">Billing Address</h2>
                  </div>

                  <label className="flex items-center space-x-2 cursor-pointer">
                    <input
                      type="checkbox"
                      checked={sameAsShipping}
                      onChange={(e) => setSameAsShipping(e.target.checked)}
                      className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                    />
                    <span className="text-sm text-gray-700">Same as shipping address</span>
                  </label>
                </div>

                {!sameAsShipping && (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {/* Billing address fields - similar to shipping */}
                    <div>
                      <label className="label">First Name *</label>
                      <input
                        {...register('billingAddress.firstName', { required: 'First name is required' })}
                        className={`input ${errors.billingAddress?.firstName ? 'input-error' : ''}`}
                      />
                    </div>
                    {/* Add other billing fields as needed */}
                  </div>
                )}
              </div>

              {/* Payment */}
              <div className="bg-white rounded-lg shadow-sm p-6">
                <div className="flex items-center space-x-2 mb-6">
                  <Lock className="h-5 w-5 text-primary-600" />
                  <h2 className="text-xl font-semibold text-gray-900">Payment Information</h2>
                </div>

                <div className="mb-4">
                  <label className="label">Card Information *</label>
                  <div className="border rounded-md p-3">
                    <CardElement
                      options={{
                        style: {
                          base: {
                            fontSize: '16px',
                            color: '#424770',
                            '::placeholder': {
                              color: '#aab7c4',
                            },
                          },
                        },
                      }}
                    />
                  </div>
                </div>

                <div className="flex items-center space-x-2 text-sm text-gray-600">
                  <Lock className="h-4 w-4" />
                  <span>Your payment information is secure and encrypted</span>
                </div>
              </div>

              {/* Order Notes */}
              <div className="bg-white rounded-lg shadow-sm p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Order Notes (Optional)</h3>
                <textarea
                  {...register('customerNotes')}
                  rows={3}
                  className="input"
                  placeholder="Special instructions for your order..."
                />
              </div>
            </div>

            {/* Order Summary */}
            <div className="lg:col-span-1">
              <div className="bg-white rounded-lg shadow-sm p-6 sticky top-4">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Order Summary</h3>

                {/* Cart Items */}
                <div className="space-y-3 mb-6 max-h-64 overflow-y-auto">
                  {cart.items.map((item) => (
                    <div key={item.product._id} className="flex items-center space-x-3">
                      <div className="w-12 h-12 bg-gray-100 rounded-lg overflow-hidden flex-shrink-0">
                        <img
                          src={item.product.images?.[0]?.url || '/api/placeholder/100/100'}
                          alt={item.product.name}
                          className="w-full h-full object-cover"
                        />
                      </div>
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium text-gray-900 truncate">
                          {item.product.name}
                        </p>
                        <p className="text-xs text-gray-600">Qty: {item.quantity}</p>
                      </div>
                      <span className="text-sm font-medium text-gray-900">
                        {formatPrice(item.priceAtTime * item.quantity)}
                      </span>
                    </div>
                  ))}
                </div>

                {/* Totals */}
                <div className="space-y-3 mb-6">
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600">Subtotal</span>
                    <span className="font-medium">{formatPrice(cart.subtotal)}</span>
                  </div>

                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600">Shipping</span>
                    <span className="font-medium">
                      {cart.estimatedShipping === 0 ? 'FREE' : formatPrice(cart.estimatedShipping)}
                    </span>
                  </div>

                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600">Tax</span>
                    <span className="font-medium">{formatPrice(cart.estimatedTax)}</span>
                  </div>

                  <div className="border-t pt-3">
                    <div className="flex justify-between">
                      <span className="text-lg font-semibold text-gray-900">Total</span>
                      <span className="text-lg font-semibold text-gray-900">
                        {paymentIntent ? formatPrice(paymentIntent.amount) : formatPrice(cart.estimatedTotal)}
                      </span>
                    </div>
                  </div>
                </div>

                {/* Place Order Button */}
                <button
                  type="submit"
                  disabled={!stripe || processing}
                  className="w-full btn-primary disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2"
                >
                  {processing ? (
                    <>
                      <LoadingSpinner size="sm" color="white" />
                      <span>Processing...</span>
                    </>
                  ) : (
                    <>
                      <Lock className="h-4 w-4" />
                      <span>Place Order</span>
                    </>
                  )}
                </button>

                <p className="text-xs text-gray-500 text-center mt-4">
                  By placing your order, you agree to our Terms of Service and Privacy Policy.
                </p>
              </div>
            </div>
          </div>
        </form>
      </div>
    </div>
  );
};

export default CheckoutPage;
