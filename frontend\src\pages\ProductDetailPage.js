import React, { useState } from 'react';
import { use<PERSON>ara<PERSON>, <PERSON>, useNavigate } from 'react-router-dom';
import { useQuery } from 'react-query';
import { productsAPI, formatPrice } from '../utils/api';
import { useAuth } from '../contexts/AuthContext';
import { useCart } from '../contexts/CartContext';
import {
  ShoppingCart,
  Heart,
  Share2,
  Star,
  Check,
  Upload,
  ChevronLeft,
  ChevronRight,
  Eye,
  Shield,
  Truck,
  RotateCcw
} from 'lucide-react';
import LoadingSpinner from '../components/ui/LoadingSpinner';
import toast from 'react-hot-toast';

const ProductDetailPage = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const { isAuthenticated } = useAuth();
  const { addToCart, isInCart, getCartItem } = useCart();

  const [selectedImageIndex, setSelectedImageIndex] = useState(0);
  const [quantity, setQuantity] = useState(1);
  const [selectedLensOptions, setSelectedLensOptions] = useState({
    blueLight: false,
    antiReflective: false,
    photochromic: false,
    polarized: false
  });
  const [showPrescriptionUpload, setShowPrescriptionUpload] = useState(false);
  const [prescriptionFile, setPrescriptionFile] = useState(null);

  const { data: product, isLoading, error } = useQuery(
    ['product', id],
    () => productsAPI.getProduct(id),
    {
      select: (response) => response.data.data.product,
      enabled: !!id
    }
  );

  const handleAddToCart = async () => {
    if (!isAuthenticated) {
      toast.error('Please login to add items to cart');
      navigate('/login');
      return;
    }

    if (!product) return;

    const cartItem = {
      productId: product._id,
      quantity,
      lensOptions: selectedLensOptions,
      prescriptionFile: prescriptionFile ? {
        filename: prescriptionFile.name,
        originalName: prescriptionFile.name,
        mimetype: prescriptionFile.type,
        size: prescriptionFile.size
      } : null
    };

    const result = await addToCart(cartItem);
    if (result.success) {
      toast.success('Added to cart successfully!');
    }
  };

  const handleLensOptionChange = (option) => {
    setSelectedLensOptions(prev => ({
      ...prev,
      [option]: !prev[option]
    }));
  };

  const handlePrescriptionUpload = (event) => {
    const file = event.target.files[0];
    if (file) {
      // Validate file type
      const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'application/pdf'];
      if (!allowedTypes.includes(file.type)) {
        toast.error('Please upload a JPG, PNG, or PDF file');
        return;
      }

      // Validate file size (5MB max)
      if (file.size > 5 * 1024 * 1024) {
        toast.error('File size must be less than 5MB');
        return;
      }

      setPrescriptionFile(file);
      toast.success('Prescription file uploaded successfully');
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <LoadingSpinner size="lg" text="Loading product..." />
      </div>
    );
  }

  if (error || !product) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Product Not Found</h1>
          <p className="text-gray-600 mb-8">The product you're looking for doesn't exist.</p>
          <Link to="/products" className="btn-primary">
            Browse Products
          </Link>
        </div>
      </div>
    );
  }

  const inCart = isInCart(product._id);
  const cartItem = getCartItem(product._id);

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container py-8">
        {/* Breadcrumb */}
        <nav className="flex items-center space-x-2 text-sm text-gray-600 mb-8">
          <Link to="/" className="hover:text-primary-600">Home</Link>
          <span>/</span>
          <Link to="/products" className="hover:text-primary-600">Products</Link>
          <span>/</span>
          <span className="text-gray-900">{product.name}</span>
        </nav>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
          {/* Product Images */}
          <div className="space-y-4">
            {/* Main Image */}
            <div className="aspect-square bg-white rounded-lg overflow-hidden shadow-sm">
              <img
                src={product.images?.[selectedImageIndex]?.url || '/api/placeholder/600/600'}
                alt={product.name}
                className="w-full h-full object-cover"
              />
            </div>

            {/* Thumbnail Images */}
            {product.images && product.images.length > 1 && (
              <div className="flex space-x-2 overflow-x-auto">
                {product.images.map((image, index) => (
                  <button
                    key={index}
                    onClick={() => setSelectedImageIndex(index)}
                    className={`flex-shrink-0 w-20 h-20 rounded-lg overflow-hidden border-2 ${
                      selectedImageIndex === index ? 'border-primary-600' : 'border-gray-200'
                    }`}
                  >
                    <img
                      src={image.url}
                      alt={`${product.name} ${index + 1}`}
                      className="w-full h-full object-cover"
                    />
                  </button>
                ))}
              </div>
            )}
          </div>

          {/* Product Info */}
          <div className="space-y-6">
            {/* Header */}
            <div>
              <div className="flex items-center justify-between mb-2">
                <span className="badge badge-primary">{product.category}</span>
                <div className="flex items-center space-x-2">
                  <button className="p-2 text-gray-400 hover:text-red-500 transition-colors">
                    <Heart className="h-5 w-5" />
                  </button>
                  <button className="p-2 text-gray-400 hover:text-gray-600 transition-colors">
                    <Share2 className="h-5 w-5" />
                  </button>
                </div>
              </div>

              <h1 className="text-3xl font-bold text-gray-900 mb-2">{product.name}</h1>

              {/* Rating */}
              <div className="flex items-center space-x-2 mb-4">
                <div className="flex items-center">
                  {[...Array(5)].map((_, i) => (
                    <Star
                      key={i}
                      className={`h-4 w-4 ${
                        i < Math.floor(product.ratings?.average || 0)
                          ? 'text-yellow-400 fill-current'
                          : 'text-gray-300'
                      }`}
                    />
                  ))}
                </div>
                <span className="text-sm text-gray-600">
                  ({product.ratings?.count || 0} reviews)
                </span>
              </div>

              {/* Price */}
              <div className="flex items-center space-x-3 mb-6">
                <span className="text-3xl font-bold text-gray-900">
                  {formatPrice(product.price)}
                </span>
                {product.comparePrice && (
                  <>
                    <span className="text-xl text-gray-500 line-through">
                      {formatPrice(product.comparePrice)}
                    </span>
                    <span className="badge badge-danger">
                      -{product.discountPercentage}% OFF
                    </span>
                  </>
                )}
              </div>
            </div>

            {/* Description */}
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Description</h3>
              <p className="text-gray-600 leading-relaxed">{product.description}</p>
            </div>

            {/* Specifications */}
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-3">Specifications</h3>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="text-gray-600">Frame Style:</span>
                  <span className="ml-2 font-medium text-gray-900 capitalize">{product.frameStyle}</span>
                </div>
                <div>
                  <span className="text-gray-600">Material:</span>
                  <span className="ml-2 font-medium text-gray-900 capitalize">{product.material}</span>
                </div>
                <div>
                  <span className="text-gray-600">Color:</span>
                  <span className="ml-2 font-medium text-gray-900">{product.color}</span>
                </div>
                <div>
                  <span className="text-gray-600">Gender:</span>
                  <span className="ml-2 font-medium text-gray-900 capitalize">{product.gender}</span>
                </div>
                {product.size && (
                  <>
                    <div>
                      <span className="text-gray-600">Lens Width:</span>
                      <span className="ml-2 font-medium text-gray-900">{product.size.lensWidth}mm</span>
                    </div>
                    <div>
                      <span className="text-gray-600">Bridge:</span>
                      <span className="ml-2 font-medium text-gray-900">{product.size.bridgeWidth}mm</span>
                    </div>
                    <div>
                      <span className="text-gray-600">Temple:</span>
                      <span className="ml-2 font-medium text-gray-900">{product.size.templeLength}mm</span>
                    </div>
                  </>
                )}
              </div>
            </div>

            {/* Lens Options */}
            {product.lensOptions && (
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-3">Lens Options</h3>
                <div className="space-y-3">
                  {product.lensOptions.blueLight && (
                    <label className="flex items-center space-x-3 cursor-pointer">
                      <input
                        type="checkbox"
                        checked={selectedLensOptions.blueLight}
                        onChange={() => handleLensOptionChange('blueLight')}
                        className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                      />
                      <span className="text-gray-700">Blue Light Protection (+$25)</span>
                    </label>
                  )}
                  {product.lensOptions.antiReflective && (
                    <label className="flex items-center space-x-3 cursor-pointer">
                      <input
                        type="checkbox"
                        checked={selectedLensOptions.antiReflective}
                        onChange={() => handleLensOptionChange('antiReflective')}
                        className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                      />
                      <span className="text-gray-700">Anti-Reflective Coating (+$35)</span>
                    </label>
                  )}
                  {product.lensOptions.photochromic && (
                    <label className="flex items-center space-x-3 cursor-pointer">
                      <input
                        type="checkbox"
                        checked={selectedLensOptions.photochromic}
                        onChange={() => handleLensOptionChange('photochromic')}
                        className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                      />
                      <span className="text-gray-700">Photochromic Lenses (+$75)</span>
                    </label>
                  )}
                  {product.lensOptions.polarized && (
                    <label className="flex items-center space-x-3 cursor-pointer">
                      <input
                        type="checkbox"
                        checked={selectedLensOptions.polarized}
                        onChange={() => handleLensOptionChange('polarized')}
                        className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                      />
                      <span className="text-gray-700">Polarized Lenses (+$50)</span>
                    </label>
                  )}
                </div>
              </div>
            )}

            {/* Prescription Upload */}
            {product.category === 'prescription' && (
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-3">Prescription</h3>
                <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                  {prescriptionFile ? (
                    <div className="flex items-center justify-center space-x-2 text-green-600">
                      <Check className="h-5 w-5" />
                      <span>{prescriptionFile.name}</span>
                      <button
                        onClick={() => setPrescriptionFile(null)}
                        className="text-red-500 hover:text-red-700"
                      >
                        Remove
                      </button>
                    </div>
                  ) : (
                    <div>
                      <Upload className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                      <p className="text-gray-600 mb-2">Upload your prescription</p>
                      <p className="text-xs text-gray-500 mb-4">JPG, PNG, or PDF (max 5MB)</p>
                      <label className="btn-outline cursor-pointer">
                        Choose File
                        <input
                          type="file"
                          accept=".jpg,.jpeg,.png,.pdf"
                          onChange={handlePrescriptionUpload}
                          className="hidden"
                        />
                      </label>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Quantity and Add to Cart */}
            <div className="space-y-4">
              <div className="flex items-center space-x-4">
                <label className="text-sm font-medium text-gray-700">Quantity:</label>
                <div className="flex items-center border rounded-md">
                  <button
                    onClick={() => setQuantity(Math.max(1, quantity - 1))}
                    className="p-2 hover:bg-gray-100"
                  >
                    -
                  </button>
                  <span className="px-4 py-2 border-x">{quantity}</span>
                  <button
                    onClick={() => setQuantity(Math.min(10, quantity + 1))}
                    className="p-2 hover:bg-gray-100"
                  >
                    +
                  </button>
                </div>
              </div>

              <div className="flex space-x-4">
                <button
                  onClick={handleAddToCart}
                  disabled={product.stockStatus === 'out-of-stock'}
                  className="flex-1 btn-primary flex items-center justify-center space-x-2 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <ShoppingCart className="h-5 w-5" />
                  <span>
                    {inCart ? `Update Cart (${cartItem?.quantity})` : 'Add to Cart'}
                  </span>
                </button>

                <Link to="/cart" className="btn-outline">
                  View Cart
                </Link>
              </div>

              {/* Stock Status */}
              <div className="flex items-center space-x-2">
                <div className={`w-2 h-2 rounded-full ${
                  product.stockStatus === 'in-stock' ? 'bg-green-500' :
                  product.stockStatus === 'low-stock' ? 'bg-yellow-500' :
                  'bg-red-500'
                }`} />
                <span className={`text-sm ${
                  product.stockStatus === 'in-stock' ? 'text-green-600' :
                  product.stockStatus === 'low-stock' ? 'text-yellow-600' :
                  'text-red-600'
                }`}>
                  {product.stockStatus === 'in-stock' ? 'In Stock' :
                   product.stockStatus === 'low-stock' ? 'Low Stock' :
                   'Out of Stock'}
                </span>
              </div>
            </div>

            {/* Features */}
            <div className="border-t pt-6">
              <div className="grid grid-cols-2 gap-4">
                <div className="flex items-center space-x-2 text-sm text-gray-600">
                  <Eye className="h-4 w-4 text-primary-600" />
                  <span>UV Protection</span>
                </div>
                <div className="flex items-center space-x-2 text-sm text-gray-600">
                  <Shield className="h-4 w-4 text-primary-600" />
                  <span>1-Year Warranty</span>
                </div>
                <div className="flex items-center space-x-2 text-sm text-gray-600">
                  <Truck className="h-4 w-4 text-primary-600" />
                  <span>Free Shipping</span>
                </div>
                <div className="flex items-center space-x-2 text-sm text-gray-600">
                  <RotateCcw className="h-4 w-4 text-primary-600" />
                  <span>30-Day Returns</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Related Products */}
        <div className="mt-16">
          <h2 className="text-2xl font-bold text-gray-900 mb-8">You Might Also Like</h2>
          {/* This would be populated with related products */}
          <div className="text-center py-8 text-gray-500">
            Related products coming soon...
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProductDetailPage;
