import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { formatPrice, formatDateTime } from '../utils/api';
import { CheckCircle, Package, Mail, ArrowRight, Download } from 'lucide-react';

const OrderSuccessPage = () => {
  const location = useLocation();
  const order = location.state?.order;

  if (!order) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Order Not Found</h1>
          <p className="text-gray-600 mb-8">We couldn't find your order information.</p>
          <Link to="/orders" className="btn-primary">
            View Orders
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container py-16">
        <div className="max-w-3xl mx-auto">
          {/* Success Header */}
          <div className="text-center mb-12">
            <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <CheckCircle className="h-8 w-8 text-green-600" />
            </div>
            <h1 className="text-3xl font-bold text-gray-900 mb-2">Order Confirmed!</h1>
            <p className="text-lg text-gray-600">
              Thank you for your purchase. Your order has been successfully placed.
            </p>
          </div>

          {/* Order Details Card */}
          <div className="bg-white rounded-lg shadow-sm p-8 mb-8">
            <div className="border-b pb-6 mb-6">
              <div className="flex items-center justify-between">
                <div>
                  <h2 className="text-xl font-semibold text-gray-900 mb-1">
                    Order #{order.orderNumber}
                  </h2>
                  <p className="text-gray-600">
                    Placed on {formatDateTime(order.orderDate)}
                  </p>
                </div>
                <div className="text-right">
                  <p className="text-2xl font-bold text-gray-900">
                    {formatPrice(order.total)}
                  </p>
                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                    {order.status}
                  </span>
                </div>
              </div>
            </div>

            {/* Order Items */}
            <div className="mb-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Order Items</h3>
              <div className="space-y-4">
                {order.items.map((item, index) => (
                  <div key={index} className="flex items-center space-x-4 p-4 bg-gray-50 rounded-lg">
                    <div className="w-16 h-16 bg-white rounded-lg overflow-hidden flex-shrink-0">
                      <img
                        src={item.productSnapshot.images?.[0] || '/api/placeholder/100/100'}
                        alt={item.productSnapshot.name}
                        className="w-full h-full object-cover"
                      />
                    </div>
                    <div className="flex-1">
                      <h4 className="font-medium text-gray-900">{item.productSnapshot.name}</h4>
                      <p className="text-sm text-gray-600">SKU: {item.productSnapshot.sku}</p>
                      <p className="text-sm text-gray-600">Quantity: {item.quantity}</p>

                      {/* Lens Options */}
                      {item.lensOptions && Object.values(item.lensOptions).some(Boolean) && (
                        <div className="mt-2 flex flex-wrap gap-1">
                          {item.lensOptions.blueLight && (
                            <span className="badge badge-primary text-xs">Blue Light</span>
                          )}
                          {item.lensOptions.antiReflective && (
                            <span className="badge badge-primary text-xs">Anti-Reflective</span>
                          )}
                          {item.lensOptions.photochromic && (
                            <span className="badge badge-primary text-xs">Photochromic</span>
                          )}
                          {item.lensOptions.polarized && (
                            <span className="badge badge-primary text-xs">Polarized</span>
                          )}
                        </div>
                      )}

                      {/* Prescription File */}
                      {item.prescriptionFile && (
                        <div className="mt-2 flex items-center space-x-2 text-sm text-green-600">
                          <CheckCircle className="h-4 w-4" />
                          <span>Prescription uploaded</span>
                        </div>
                      )}
                    </div>
                    <div className="text-right">
                      <p className="font-medium text-gray-900">
                        {formatPrice(item.price * item.quantity)}
                      </p>
                      <p className="text-sm text-gray-600">
                        {formatPrice(item.price)} each
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Order Summary */}
            <div className="border-t pt-6">
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Subtotal</span>
                  <span className="font-medium">{formatPrice(order.subtotal)}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Shipping</span>
                  <span className="font-medium">
                    {order.shipping === 0 ? 'FREE' : formatPrice(order.shipping)}
                  </span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Tax</span>
                  <span className="font-medium">{formatPrice(order.tax)}</span>
                </div>
                {order.discount > 0 && (
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600">Discount</span>
                    <span className="font-medium text-green-600">
                      -{formatPrice(order.discount)}
                    </span>
                  </div>
                )}
                <div className="flex justify-between text-lg font-semibold border-t pt-2">
                  <span>Total</span>
                  <span>{formatPrice(order.total)}</span>
                </div>
              </div>
            </div>
          </div>

          {/* Shipping Information */}
          <div className="bg-white rounded-lg shadow-sm p-6 mb-8">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Shipping Information</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h4 className="font-medium text-gray-900 mb-2">Shipping Address</h4>
                <div className="text-sm text-gray-600">
                  <p>{order.shippingAddress.firstName} {order.shippingAddress.lastName}</p>
                  <p>{order.shippingAddress.address1}</p>
                  {order.shippingAddress.address2 && <p>{order.shippingAddress.address2}</p>}
                  <p>
                    {order.shippingAddress.city}, {order.shippingAddress.state} {order.shippingAddress.zipCode}
                  </p>
                  <p>{order.shippingAddress.country}</p>
                </div>
              </div>
              <div>
                <h4 className="font-medium text-gray-900 mb-2">Shipping Method</h4>
                <p className="text-sm text-gray-600 capitalize">
                  {order.shippingMethod} Shipping
                </p>
                {order.estimatedDelivery && (
                  <p className="text-sm text-gray-600">
                    Estimated delivery: {formatDateTime(order.estimatedDelivery)}
                  </p>
                )}
              </div>
            </div>
          </div>

          {/* Next Steps */}
          <div className="bg-blue-50 rounded-lg p-6 mb-8">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">What's Next?</h3>
            <div className="space-y-3">
              <div className="flex items-start space-x-3">
                <Mail className="h-5 w-5 text-blue-600 mt-0.5" />
                <div>
                  <p className="font-medium text-gray-900">Order Confirmation Email</p>
                  <p className="text-sm text-gray-600">
                    We've sent a confirmation email to {order.shippingAddress.email || 'your email address'}
                  </p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <Package className="h-5 w-5 text-blue-600 mt-0.5" />
                <div>
                  <p className="font-medium text-gray-900">Processing Your Order</p>
                  <p className="text-sm text-gray-600">
                    We'll start processing your order and will notify you when it ships
                  </p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <CheckCircle className="h-5 w-5 text-blue-600 mt-0.5" />
                <div>
                  <p className="font-medium text-gray-900">Track Your Order</p>
                  <p className="text-sm text-gray-600">
                    You can track your order status in your account dashboard
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              to={`/orders/${order._id}`}
              className="btn-primary flex items-center justify-center space-x-2"
            >
              <Package className="h-4 w-4" />
              <span>Track Order</span>
            </Link>
            <Link
              to="/products"
              className="btn-outline flex items-center justify-center space-x-2"
            >
              <span>Continue Shopping</span>
              <ArrowRight className="h-4 w-4" />
            </Link>
          </div>

          {/* Support */}
          <div className="text-center mt-12 pt-8 border-t">
            <p className="text-gray-600 mb-4">
              Need help with your order?
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <a
                href="mailto:<EMAIL>"
                className="text-primary-600 hover:text-primary-700 font-medium"
              >
                Email Support
              </a>
              <a
                href="tel:1-800-IVIO-EYE"
                className="text-primary-600 hover:text-primary-700 font-medium"
              >
                Call 1-800-IVIO-EYE
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default OrderSuccessPage;
