{"name": "ivio-glasses-frontend", "version": "1.0.0", "description": "Frontend React app for IVIO Prescription Glasses Marketplace", "private": true, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.15.0", "axios": "^1.5.0", "react-hook-form": "^7.45.4", "@stripe/stripe-js": "^2.1.6", "@stripe/react-stripe-js": "^2.1.1", "react-query": "^3.39.3", "react-hot-toast": "^2.4.1", "lucide-react": "^0.279.0", "clsx": "^2.0.0", "web-vitals": "^2.1.4"}, "devDependencies": {"react-scripts": "5.0.1", "tailwindcss": "^3.3.3", "autoprefixer": "^10.4.15", "postcss": "^8.4.29", "@testing-library/react": "^13.4.0", "@testing-library/jest-dom": "^5.17.0", "@testing-library/user-event": "^13.5.0"}, "scripts": {"start": "set PORT=3000 && react-scripts start", "start:unix": "PORT=3000 react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "dev": "npm run start", "install-deps": "npm install --legacy-peer-deps"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "proxy": "http://localhost:5000"}