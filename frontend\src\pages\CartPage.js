import React, { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useCart } from '../contexts/CartContext';
import { useAuth } from '../contexts/AuthContext';
import { formatPrice, uploadAPI } from '../utils/api';
import {
  Trash2,
  Plus,
  Minus,
  Upload,
  Check,
  X,
  ShoppingBag,
  ArrowRight,
  Eye,
  Download
} from 'lucide-react';
import LoadingSpinner from '../components/ui/LoadingSpinner';
import toast from 'react-hot-toast';

const CartPage = () => {
  const { cart, updateCartItem, removeFromCart, clearCart, loading } = useCart();
  const { isAuthenticated } = useAuth();
  const navigate = useNavigate();
  const [uploadingPrescription, setUploadingPrescription] = useState({});

  const handleQuantityChange = async (productId, newQuantity) => {
    if (newQuantity < 1) {
      await removeFromCart(productId);
    } else {
      await updateCartItem(productId, newQuantity);
    }
  };

  const handlePrescriptionUpload = async (productId, file) => {
    if (!file) return;

    // Validate file
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'application/pdf'];
    if (!allowedTypes.includes(file.type)) {
      toast.error('Please upload a JPG, PNG, or PDF file');
      return;
    }

    if (file.size > 5 * 1024 * 1024) {
      toast.error('File size must be less than 5MB');
      return;
    }

    setUploadingPrescription(prev => ({ ...prev, [productId]: true }));

    try {
      const response = await uploadAPI.uploadPrescription(file);
      const fileInfo = response.data.data.file;

      // Update cart item with prescription file info
      // This would typically be handled by updating the cart item
      toast.success('Prescription uploaded successfully');
    } catch (error) {
      toast.error('Failed to upload prescription');
    } finally {
      setUploadingPrescription(prev => ({ ...prev, [productId]: false }));
    }
  };

  const handleCheckout = () => {
    if (!isAuthenticated) {
      toast.error('Please login to proceed to checkout');
      navigate('/login');
      return;
    }

    // Check if prescription glasses need prescriptions
    const prescriptionItems = cart?.items?.filter(item =>
      item.product.category === 'prescription' && !item.prescriptionFile
    );

    if (prescriptionItems && prescriptionItems.length > 0) {
      toast.error('Please upload prescriptions for all prescription glasses');
      return;
    }

    navigate('/checkout');
  };

  if (!isAuthenticated) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <ShoppingBag className="h-16 w-16 text-gray-400 mx-auto mb-4" />
          <h2 className="text-2xl font-bold text-gray-900 mb-4">Please Login</h2>
          <p className="text-gray-600 mb-8">You need to be logged in to view your cart.</p>
          <Link to="/login" className="btn-primary">
            Login
          </Link>
        </div>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <LoadingSpinner size="lg" text="Loading cart..." />
      </div>
    );
  }

  if (!cart || !cart.items || cart.items.length === 0) {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="container py-16">
          <div className="text-center">
            <ShoppingBag className="h-16 w-16 text-gray-400 mx-auto mb-4" />
            <h2 className="text-2xl font-bold text-gray-900 mb-4">Your cart is empty</h2>
            <p className="text-gray-600 mb-8">Looks like you haven't added any items to your cart yet.</p>
            <Link to="/products" className="btn-primary">
              Start Shopping
            </Link>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container py-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <h1 className="text-3xl font-bold text-gray-900">Shopping Cart</h1>
          <button
            onClick={clearCart}
            className="text-red-600 hover:text-red-700 text-sm font-medium"
          >
            Clear Cart
          </button>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Cart Items */}
          <div className="lg:col-span-2 space-y-4">
            {cart.items.map((item) => (
              <CartItem
                key={item.product._id}
                item={item}
                onQuantityChange={handleQuantityChange}
                onRemove={removeFromCart}
                onPrescriptionUpload={handlePrescriptionUpload}
                uploadingPrescription={uploadingPrescription[item.product._id]}
              />
            ))}
          </div>

          {/* Order Summary */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-lg shadow-sm p-6 sticky top-4">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Order Summary</h3>

              <div className="space-y-3 mb-6">
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Subtotal ({cart.totalItems} items)</span>
                  <span className="font-medium">{formatPrice(cart.subtotal)}</span>
                </div>

                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Estimated Tax</span>
                  <span className="font-medium">{formatPrice(cart.estimatedTax)}</span>
                </div>

                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Shipping</span>
                  <span className="font-medium">
                    {cart.estimatedShipping === 0 ? 'FREE' : formatPrice(cart.estimatedShipping)}
                  </span>
                </div>

                {cart.estimatedShipping === 0 && cart.subtotal < 100 && (
                  <p className="text-xs text-green-600">
                    Free shipping on orders over $100
                  </p>
                )}

                <div className="border-t pt-3">
                  <div className="flex justify-between">
                    <span className="text-lg font-semibold text-gray-900">Total</span>
                    <span className="text-lg font-semibold text-gray-900">
                      {formatPrice(cart.estimatedTotal)}
                    </span>
                  </div>
                </div>
              </div>

              <button
                onClick={handleCheckout}
                className="w-full btn-primary flex items-center justify-center space-x-2"
              >
                <span>Proceed to Checkout</span>
                <ArrowRight className="h-4 w-4" />
              </button>

              <div className="mt-4 text-center">
                <Link
                  to="/products"
                  className="text-primary-600 hover:text-primary-700 text-sm font-medium"
                >
                  Continue Shopping
                </Link>
              </div>

              {/* Security Features */}
              <div className="mt-6 pt-6 border-t">
                <div className="flex items-center space-x-2 text-xs text-gray-500 mb-2">
                  <Eye className="h-3 w-3" />
                  <span>Secure checkout with SSL encryption</span>
                </div>
                <div className="flex items-center space-x-2 text-xs text-gray-500">
                  <Check className="h-3 w-3" />
                  <span>30-day return policy</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

// Cart Item Component
const CartItem = ({
  item,
  onQuantityChange,
  onRemove,
  onPrescriptionUpload,
  uploadingPrescription
}) => {
  const [prescriptionFile, setPrescriptionFile] = useState(item.prescriptionFile);
  const product = item.product;
  const primaryImage = product.images?.find(img => img.isPrimary) || product.images?.[0];

  const handleFileUpload = (event) => {
    const file = event.target.files[0];
    if (file) {
      setPrescriptionFile({
        filename: file.name,
        originalName: file.name,
        mimetype: file.type,
        size: file.size
      });
      onPrescriptionUpload(product._id, file);
    }
  };

  const totalPrice = item.priceAtTime * item.quantity;

  return (
    <div className="bg-white rounded-lg shadow-sm p-6">
      <div className="flex items-start space-x-4">
        {/* Product Image */}
        <div className="w-24 h-24 bg-gray-100 rounded-lg overflow-hidden flex-shrink-0">
          <img
            src={primaryImage?.url || '/api/placeholder/200/200'}
            alt={product.name}
            className="w-full h-full object-cover"
          />
        </div>

        {/* Product Details */}
        <div className="flex-1 min-w-0">
          <div className="flex items-start justify-between">
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-1">
                <Link
                  to={`/products/${product._id}`}
                  className="hover:text-primary-600"
                >
                  {product.name}
                </Link>
              </h3>
              <p className="text-sm text-gray-600 mb-2">
                {product.frameStyle} • {product.material} • {product.color}
              </p>
              <div className="flex items-center space-x-4 text-sm text-gray-600">
                <span>SKU: {product.sku}</span>
                <span className="badge badge-primary text-xs">{product.category}</span>
              </div>
            </div>

            <button
              onClick={() => onRemove(product._id)}
              className="text-gray-400 hover:text-red-500 transition-colors"
            >
              <Trash2 className="h-5 w-5" />
            </button>
          </div>

          {/* Lens Options */}
          {item.lensOptions && Object.values(item.lensOptions).some(Boolean) && (
            <div className="mt-3">
              <p className="text-sm font-medium text-gray-700 mb-1">Lens Options:</p>
              <div className="flex flex-wrap gap-2">
                {item.lensOptions.blueLight && (
                  <span className="badge badge-primary text-xs">Blue Light</span>
                )}
                {item.lensOptions.antiReflective && (
                  <span className="badge badge-primary text-xs">Anti-Reflective</span>
                )}
                {item.lensOptions.photochromic && (
                  <span className="badge badge-primary text-xs">Photochromic</span>
                )}
                {item.lensOptions.polarized && (
                  <span className="badge badge-primary text-xs">Polarized</span>
                )}
              </div>
            </div>
          )}

          {/* Prescription Upload */}
          {product.category === 'prescription' && (
            <div className="mt-4">
              <p className="text-sm font-medium text-gray-700 mb-2">Prescription:</p>
              {prescriptionFile ? (
                <div className="flex items-center space-x-2 text-sm">
                  <Check className="h-4 w-4 text-green-500" />
                  <span className="text-green-600">{prescriptionFile.originalName}</span>
                  <button
                    onClick={() => setPrescriptionFile(null)}
                    className="text-red-500 hover:text-red-700"
                  >
                    <X className="h-4 w-4" />
                  </button>
                </div>
              ) : (
                <div className="border-2 border-dashed border-gray-300 rounded-lg p-3">
                  {uploadingPrescription ? (
                    <div className="flex items-center justify-center space-x-2">
                      <LoadingSpinner size="sm" />
                      <span className="text-sm text-gray-600">Uploading...</span>
                    </div>
                  ) : (
                    <label className="flex items-center justify-center space-x-2 cursor-pointer text-sm text-gray-600 hover:text-primary-600">
                      <Upload className="h-4 w-4" />
                      <span>Upload Prescription</span>
                      <input
                        type="file"
                        accept=".jpg,.jpeg,.png,.pdf"
                        onChange={handleFileUpload}
                        className="hidden"
                      />
                    </label>
                  )}
                </div>
              )}
            </div>
          )}

          {/* Quantity and Price */}
          <div className="flex items-center justify-between mt-4">
            <div className="flex items-center space-x-3">
              <span className="text-sm font-medium text-gray-700">Qty:</span>
              <div className="flex items-center border rounded-md">
                <button
                  onClick={() => onQuantityChange(product._id, item.quantity - 1)}
                  className="p-1 hover:bg-gray-100 disabled:opacity-50"
                  disabled={item.quantity <= 1}
                >
                  <Minus className="h-4 w-4" />
                </button>
                <span className="px-3 py-1 border-x text-sm font-medium">
                  {item.quantity}
                </span>
                <button
                  onClick={() => onQuantityChange(product._id, item.quantity + 1)}
                  className="p-1 hover:bg-gray-100 disabled:opacity-50"
                  disabled={item.quantity >= 10}
                >
                  <Plus className="h-4 w-4" />
                </button>
              </div>
            </div>

            <div className="text-right">
              <div className="text-lg font-semibold text-gray-900">
                {formatPrice(totalPrice)}
              </div>
              <div className="text-sm text-gray-500">
                {formatPrice(item.priceAtTime)} each
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CartPage;
