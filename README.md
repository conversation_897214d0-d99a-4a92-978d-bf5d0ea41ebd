# IVIO Prescription Glasses Marketplace

A full-stack web application for selling prescription glasses with modern features including user authentication, product browsing, cart management, secure checkout, and admin panel.

## Features

- **Authentication**: Email/password signup, login, password reset
- **Product Browsing**: Homepage with featured glasses, search and filter functionality
- **Product Details**: Multiple images, descriptions, prescription upload (PDF/JPG/PNG)
- **Shopping Cart**: Add/remove items, quantity management, subtotal calculation
- **Secure Checkout**: Stripe payment integration
- **Order Tracking**: User dashboard with order history and status
- **Admin Panel**: Product management, inventory control, order status updates
- **Responsive Design**: Mobile-friendly UI with clean navigation

## Tech Stack

### Frontend
- React 18
- React Router for navigation
- Axios for API calls
- Tailwind CSS for styling
- React Hook Form for form handling

### Backend
- Node.js with Express
- MongoDB with Mongoose
- JWT for authentication
- Multer for file uploads
- Stripe for payments
- Bcrypt for password hashing

## Project Structure

```
ivio-glasses/
├── backend/          # Node.js/Express API
├── frontend/         # React application
├── uploads/          # File upload storage
└── README.md
```

## Quick Start

### Prerequisites
- Node.js (v16 or higher)
- MongoDB (local or cloud)
- Stripe account for payments

### Installation

1. Clone the repository
2. Install backend dependencies:
   ```bash
   cd backend
   npm install
   ```

3. Install frontend dependencies:
   ```bash
   cd frontend
   npm install
   ```

4. Set up environment variables (see .env.example files)

5. Start the development servers:
   ```bash
   # Backend (runs on port 5000)
   cd backend
   npm run dev

   # Frontend (runs on port 3000)
   cd frontend
   npm start
   ```

## Environment Variables

### Backend (.env)
- `MONGODB_URI`: MongoDB connection string
- `JWT_SECRET`: Secret key for JWT tokens
- `STRIPE_SECRET_KEY`: Stripe secret key
- `EMAIL_SERVICE`: Email service configuration

### Frontend (.env)
- `REACT_APP_API_URL`: Backend API URL
- `REACT_APP_STRIPE_PUBLISHABLE_KEY`: Stripe publishable key

## Deployment

Basic deployment instructions for popular hosting platforms will be provided in the deployment guide.

## License

MIT License
