import React, { useState, useEffect } from 'react';
import { useQuery } from 'react-query';
import { useSearchParams, Link } from 'react-router-dom';
import { productsAPI, formatPrice } from '../utils/api';
import { Search, Filter, Grid, List, ChevronDown } from 'lucide-react';
import LoadingSpinner from '../components/ui/LoadingSpinner';

const ProductsPage = () => {
  const [searchParams, setSearchParams] = useSearchParams();
  const [viewMode, setViewMode] = useState('grid');
  const [showFilters, setShowFilters] = useState(false);
  const [searchTerm, setSearchTerm] = useState(searchParams.get('search') || '');

  const filters = {
    page: parseInt(searchParams.get('page')) || 1,
    limit: 12,
    category: searchParams.get('category') || '',
    frameStyle: searchParams.get('frameStyle') || '',
    material: searchParams.get('material') || '',
    minPrice: searchParams.get('minPrice') || '',
    maxPrice: searchParams.get('maxPrice') || '',
    gender: searchParams.get('gender') || '',
    search: searchParams.get('search') || '',
    sort: searchParams.get('sort') || 'newest'
  };

  const { data, isLoading, error } = useQuery(
    ['products', filters],
    () => productsAPI.getProducts(filters),
    {
      select: (response) => response.data.data,
      keepPreviousData: true
    }
  );

  const updateFilter = (key, value) => {
    const newParams = new URLSearchParams(searchParams);
    if (value) {
      newParams.set(key, value);
    } else {
      newParams.delete(key);
    }
    newParams.set('page', '1'); // Reset to first page when filtering
    setSearchParams(newParams);
  };

  const handleSearch = (e) => {
    e.preventDefault();
    updateFilter('search', searchTerm);
  };

  const clearFilters = () => {
    setSearchParams({});
    setSearchTerm('');
  };

  const categories = [
    { value: '', label: 'All Categories' },
    { value: 'prescription', label: 'Prescription Glasses' },
    { value: 'sunglasses', label: 'Sunglasses' },
    { value: 'reading', label: 'Reading Glasses' },
    { value: 'computer', label: 'Computer Glasses' },
    { value: 'safety', label: 'Safety Glasses' }
  ];

  const frameStyles = [
    { value: '', label: 'All Styles' },
    { value: 'aviator', label: 'Aviator' },
    { value: 'wayfarer', label: 'Wayfarer' },
    { value: 'round', label: 'Round' },
    { value: 'square', label: 'Square' },
    { value: 'cat-eye', label: 'Cat Eye' },
    { value: 'rectangular', label: 'Rectangular' },
    { value: 'oval', label: 'Oval' },
    { value: 'rimless', label: 'Rimless' }
  ];

  const materials = [
    { value: '', label: 'All Materials' },
    { value: 'acetate', label: 'Acetate' },
    { value: 'metal', label: 'Metal' },
    { value: 'titanium', label: 'Titanium' },
    { value: 'plastic', label: 'Plastic' },
    { value: 'wood', label: 'Wood' },
    { value: 'carbon-fiber', label: 'Carbon Fiber' }
  ];

  const sortOptions = [
    { value: 'newest', label: 'Newest First' },
    { value: 'price_asc', label: 'Price: Low to High' },
    { value: 'price_desc', label: 'Price: High to Low' },
    { value: 'name_asc', label: 'Name: A to Z' },
    { value: 'name_desc', label: 'Name: Z to A' },
    { value: 'rating_desc', label: 'Highest Rated' }
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">
            {filters.category ?
              categories.find(c => c.value === filters.category)?.label || 'Products' :
              'All Products'
            }
          </h1>

          {/* Search Bar */}
          <form onSubmit={handleSearch} className="mb-6">
            <div className="relative max-w-md">
              <input
                type="text"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                placeholder="Search glasses..."
                className="input pl-10 pr-4"
              />
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
              <button
                type="submit"
                className="absolute right-2 top-1/2 transform -translate-y-1/2 btn-primary btn-sm"
              >
                Search
              </button>
            </div>
          </form>
        </div>

        <div className="flex flex-col lg:flex-row gap-8">
          {/* Filters Sidebar */}
          <div className="lg:w-64 flex-shrink-0">
            <div className="bg-white rounded-lg shadow-sm p-6 sticky top-4">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-gray-900">Filters</h3>
                <button
                  onClick={clearFilters}
                  className="text-sm text-primary-600 hover:text-primary-700"
                >
                  Clear All
                </button>
              </div>

              <div className="space-y-6">
                {/* Category Filter */}
                <div>
                  <label className="label">Category</label>
                  <select
                    value={filters.category}
                    onChange={(e) => updateFilter('category', e.target.value)}
                    className="input"
                  >
                    {categories.map(category => (
                      <option key={category.value} value={category.value}>
                        {category.label}
                      </option>
                    ))}
                  </select>
                </div>

                {/* Frame Style Filter */}
                <div>
                  <label className="label">Frame Style</label>
                  <select
                    value={filters.frameStyle}
                    onChange={(e) => updateFilter('frameStyle', e.target.value)}
                    className="input"
                  >
                    {frameStyles.map(style => (
                      <option key={style.value} value={style.value}>
                        {style.label}
                      </option>
                    ))}
                  </select>
                </div>

                {/* Material Filter */}
                <div>
                  <label className="label">Material</label>
                  <select
                    value={filters.material}
                    onChange={(e) => updateFilter('material', e.target.value)}
                    className="input"
                  >
                    {materials.map(material => (
                      <option key={material.value} value={material.value}>
                        {material.label}
                      </option>
                    ))}
                  </select>
                </div>

                {/* Price Range */}
                <div>
                  <label className="label">Price Range</label>
                  <div className="grid grid-cols-2 gap-2">
                    <input
                      type="number"
                      placeholder="Min"
                      value={filters.minPrice}
                      onChange={(e) => updateFilter('minPrice', e.target.value)}
                      className="input"
                    />
                    <input
                      type="number"
                      placeholder="Max"
                      value={filters.maxPrice}
                      onChange={(e) => updateFilter('maxPrice', e.target.value)}
                      className="input"
                    />
                  </div>
                </div>

                {/* Gender Filter */}
                <div>
                  <label className="label">Gender</label>
                  <select
                    value={filters.gender}
                    onChange={(e) => updateFilter('gender', e.target.value)}
                    className="input"
                  >
                    <option value="">All</option>
                    <option value="men">Men</option>
                    <option value="women">Women</option>
                    <option value="unisex">Unisex</option>
                  </select>
                </div>
              </div>
            </div>
          </div>

          {/* Products Grid */}
          <div className="flex-1">
            {/* Toolbar */}
            <div className="bg-white rounded-lg shadow-sm p-4 mb-6">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <span className="text-sm text-gray-600">
                    {data?.totalProducts || 0} products found
                  </span>
                </div>

                <div className="flex items-center space-x-4">
                  {/* Sort */}
                  <select
                    value={filters.sort}
                    onChange={(e) => updateFilter('sort', e.target.value)}
                    className="input"
                  >
                    {sortOptions.map(option => (
                      <option key={option.value} value={option.value}>
                        {option.label}
                      </option>
                    ))}
                  </select>

                  {/* View Mode */}
                  <div className="flex items-center border rounded-md">
                    <button
                      onClick={() => setViewMode('grid')}
                      className={`p-2 ${viewMode === 'grid' ? 'bg-primary-100 text-primary-600' : 'text-gray-400'}`}
                    >
                      <Grid className="h-4 w-4" />
                    </button>
                    <button
                      onClick={() => setViewMode('list')}
                      className={`p-2 ${viewMode === 'list' ? 'bg-primary-100 text-primary-600' : 'text-gray-400'}`}
                    >
                      <List className="h-4 w-4" />
                    </button>
                  </div>
                </div>
              </div>
            </div>

            {/* Loading State */}
            {isLoading && (
              <div className="flex justify-center py-12">
                <LoadingSpinner size="lg" text="Loading products..." />
              </div>
            )}

            {/* Error State */}
            {error && (
              <div className="text-center py-12">
                <p className="text-red-600">Error loading products. Please try again.</p>
              </div>
            )}

            {/* Products */}
            {data && (
              <>
                <div className={viewMode === 'grid' ?
                  'grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6' :
                  'space-y-4'
                }>
                  {data.products.map((product) => (
                    <ProductCard
                      key={product._id}
                      product={product}
                      viewMode={viewMode}
                    />
                  ))}
                </div>

                {/* Pagination */}
                {data.pagination.totalPages > 1 && (
                  <div className="mt-8 flex justify-center">
                    <div className="flex items-center space-x-2">
                      {Array.from({ length: data.pagination.totalPages }, (_, i) => i + 1).map(page => (
                        <button
                          key={page}
                          onClick={() => updateFilter('page', page.toString())}
                          className={`px-3 py-2 rounded-md ${
                            page === data.pagination.currentPage
                              ? 'bg-primary-600 text-white'
                              : 'bg-white text-gray-700 hover:bg-gray-50'
                          }`}
                        >
                          {page}
                        </button>
                      ))}
                    </div>
                  </div>
                )}
              </>
            )}

            {/* No Results */}
            {data && data.products.length === 0 && (
              <div className="text-center py-12">
                <p className="text-gray-600 mb-4">No products found matching your criteria.</p>
                <button
                  onClick={clearFilters}
                  className="btn-primary"
                >
                  Clear Filters
                </button>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

// Product Card Component
const ProductCard = ({ product, viewMode }) => {
  const primaryImage = product.images?.find(img => img.isPrimary) || product.images?.[0];

  if (viewMode === 'list') {
    return (
      <Link to={`/products/${product._id}`} className="block">
        <div className="bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow p-4">
          <div className="flex items-center space-x-4">
            <div className="w-24 h-24 bg-gray-100 rounded-lg overflow-hidden flex-shrink-0">
              <img
                src={primaryImage?.url || '/api/placeholder/200/200'}
                alt={product.name}
                className="w-full h-full object-cover"
              />
            </div>
            <div className="flex-1">
              <h3 className="text-lg font-semibold text-gray-900 mb-1">{product.name}</h3>
              <p className="text-gray-600 text-sm mb-2 line-clamp-2">{product.shortDescription}</p>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <span className="text-xl font-bold text-gray-900">
                    {formatPrice(product.price)}
                  </span>
                  {product.comparePrice && (
                    <span className="text-sm text-gray-500 line-through">
                      {formatPrice(product.comparePrice)}
                    </span>
                  )}
                </div>
                <div className="flex items-center space-x-2">
                  <span className="badge badge-primary">{product.category}</span>
                  <span className="badge badge-gray">{product.frameStyle}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </Link>
    );
  }

  return (
    <Link to={`/products/${product._id}`} className="group">
      <div className="bg-white rounded-lg shadow-sm hover:shadow-lg transition-shadow overflow-hidden">
        <div className="aspect-square bg-gray-100 overflow-hidden">
          <img
            src={primaryImage?.url || '/api/placeholder/300/300'}
            alt={product.name}
            className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
          />
        </div>
        <div className="p-4">
          <div className="flex items-center justify-between mb-2">
            <span className="badge badge-primary text-xs">{product.category}</span>
            {product.isFeatured && (
              <span className="badge badge-warning text-xs">Featured</span>
            )}
          </div>
          <h3 className="font-semibold text-gray-900 mb-1 group-hover:text-primary-600 transition-colors">
            {product.name}
          </h3>
          <p className="text-gray-600 text-sm mb-3 line-clamp-2">
            {product.shortDescription}
          </p>
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <span className="text-lg font-bold text-gray-900">
                {formatPrice(product.price)}
              </span>
              {product.comparePrice && (
                <span className="text-sm text-gray-500 line-through">
                  {formatPrice(product.comparePrice)}
                </span>
              )}
            </div>
            {product.discountPercentage > 0 && (
              <span className="badge badge-danger text-xs">
                -{product.discountPercentage}%
              </span>
            )}
          </div>
          <div className="mt-2 flex items-center justify-between text-xs text-gray-500">
            <span>{product.frameStyle} • {product.material}</span>
            <span className={`badge ${
              product.stockStatus === 'in-stock' ? 'badge-success' :
              product.stockStatus === 'low-stock' ? 'badge-warning' :
              'badge-danger'
            } text-xs`}>
              {product.stockStatus === 'in-stock' ? 'In Stock' :
               product.stockStatus === 'low-stock' ? 'Low Stock' :
               'Out of Stock'}
            </span>
          </div>
        </div>
      </div>
    </Link>
  );
};

export default ProductsPage;
