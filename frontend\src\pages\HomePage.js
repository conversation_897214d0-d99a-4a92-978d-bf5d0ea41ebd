import React from 'react';
import { Link } from 'react-router-dom';
import { useQuery } from 'react-query';
import { productsAPI } from '../utils/api';
import { ArrowRight, Eye, Shield, Truck, Award } from 'lucide-react';
import LoadingSpinner from '../components/ui/LoadingSpinner';

const HomePage = () => {
  const { data: featuredProducts, isLoading } = useQuery(
    'featuredProducts',
    () => productsAPI.getFeaturedProducts(8),
    {
      select: (response) => response.data.data.products,
    }
  );

  const features = [
    {
      icon: Eye,
      title: 'Premium Quality',
      description: 'High-quality lenses and frames crafted with precision and care.',
    },
    {
      icon: Shield,
      title: 'UV Protection',
      description: '100% UV protection to keep your eyes safe from harmful rays.',
    },
    {
      icon: Truck,
      title: 'Free Shipping',
      description: 'Free shipping on orders over $100 with fast delivery.',
    },
    {
      icon: Award,
      title: 'Warranty',
      description: '1-year warranty on all frames and prescription lenses.',
    },
  ];

  const categories = [
    {
      name: 'Prescription Glasses',
      description: 'Clear vision with style',
      image: '/api/placeholder/400/300',
      href: '/products?category=prescription',
    },
    {
      name: 'Sunglasses',
      description: 'Protection meets fashion',
      image: '/api/placeholder/400/300',
      href: '/products?category=sunglasses',
    },
    {
      name: 'Reading Glasses',
      description: 'Comfort for close-up work',
      image: '/api/placeholder/400/300',
      href: '/products?category=reading',
    },
  ];

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative bg-gradient-to-br from-primary-50 to-primary-100 overflow-hidden">
        <div className="container py-20 lg:py-32">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div className="space-y-8">
              <div className="space-y-4">
                <h1 className="text-4xl lg:text-6xl font-bold text-gray-900 leading-tight">
                  See the World
                  <span className="text-gradient block">Clearly</span>
                </h1>
                <p className="text-xl text-gray-600 max-w-lg">
                  Premium prescription glasses with modern style and exceptional quality. 
                  Find your perfect pair from our curated collection.
                </p>
              </div>
              
              <div className="flex flex-col sm:flex-row gap-4">
                <Link to="/products" className="btn-primary btn-lg">
                  Shop Now
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Link>
                <Link to="/products?category=prescription" className="btn-outline btn-lg">
                  Upload Prescription
                </Link>
              </div>

              <div className="flex items-center space-x-8 text-sm text-gray-600">
                <div className="flex items-center space-x-2">
                  <Shield className="h-5 w-5 text-primary-600" />
                  <span>UV Protection</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Truck className="h-5 w-5 text-primary-600" />
                  <span>Free Shipping</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Award className="h-5 w-5 text-primary-600" />
                  <span>1-Year Warranty</span>
                </div>
              </div>
            </div>

            <div className="relative">
              <div className="aspect-square bg-white rounded-full shadow-2xl p-8">
                <img
                  src="/api/placeholder/500/500"
                  alt="Premium Glasses"
                  className="w-full h-full object-cover rounded-full"
                />
              </div>
              <div className="absolute -top-4 -right-4 w-24 h-24 bg-primary-600 rounded-full flex items-center justify-center text-white font-bold text-lg shadow-lg">
                NEW
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="section bg-white">
        <div className="container">
          <div className="text-center mb-16">
            <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
              Why Choose IVIO?
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              We're committed to providing the best eyewear experience with premium quality and exceptional service.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {features.map((feature, index) => (
              <div key={index} className="text-center group">
                <div className="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:bg-primary-200 transition-colors">
                  <feature.icon className="h-8 w-8 text-primary-600" />
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-2">
                  {feature.title}
                </h3>
                <p className="text-gray-600">
                  {feature.description}
                </p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Categories Section */}
      <section className="section bg-gray-50">
        <div className="container">
          <div className="text-center mb-16">
            <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
              Shop by Category
            </h2>
            <p className="text-xl text-gray-600">
              Find the perfect glasses for every need and style.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {categories.map((category, index) => (
              <Link
                key={index}
                to={category.href}
                className="group relative overflow-hidden rounded-2xl shadow-lg hover:shadow-xl transition-shadow"
              >
                <div className="aspect-[4/3] bg-gray-200">
                  <img
                    src={category.image}
                    alt={category.name}
                    className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                </div>
                <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent" />
                <div className="absolute bottom-6 left-6 text-white">
                  <h3 className="text-2xl font-bold mb-2">{category.name}</h3>
                  <p className="text-gray-200">{category.description}</p>
                </div>
              </Link>
            ))}
          </div>
        </div>
      </section>

      {/* Featured Products Section */}
      <section className="section bg-white">
        <div className="container">
          <div className="flex justify-between items-center mb-12">
            <div>
              <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
                Featured Products
              </h2>
              <p className="text-xl text-gray-600">
                Discover our most popular and trending eyewear.
              </p>
            </div>
            <Link to="/products" className="btn-outline hidden sm:flex">
              View All
              <ArrowRight className="ml-2 h-4 w-4" />
            </Link>
          </div>

          {isLoading ? (
            <div className="flex justify-center py-12">
              <LoadingSpinner size="lg" text="Loading products..." />
            </div>
          ) : (
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
              {featuredProducts?.map((product) => (
                <Link
                  key={product._id}
                  to={`/products/${product._id}`}
                  className="group"
                >
                  <div className="card overflow-hidden hover:shadow-lg transition-shadow">
                    <div className="aspect-square bg-gray-100 overflow-hidden">
                      <img
                        src={product.images?.[0]?.url || '/api/placeholder/300/300'}
                        alt={product.name}
                        className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                      />
                    </div>
                    <div className="p-4">
                      <h3 className="font-semibold text-gray-900 mb-1 group-hover:text-primary-600 transition-colors">
                        {product.name}
                      </h3>
                      <p className="text-gray-600 text-sm mb-2 line-clamp-2">
                        {product.shortDescription}
                      </p>
                      <div className="flex items-center justify-between">
                        <span className="text-lg font-bold text-gray-900">
                          ${product.price}
                        </span>
                        {product.comparePrice && (
                          <span className="text-sm text-gray-500 line-through">
                            ${product.comparePrice}
                          </span>
                        )}
                      </div>
                    </div>
                  </div>
                </Link>
              ))}
            </div>
          )}

          <div className="text-center mt-8 sm:hidden">
            <Link to="/products" className="btn-outline">
              View All Products
              <ArrowRight className="ml-2 h-4 w-4" />
            </Link>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="section bg-primary-600 text-white">
        <div className="container text-center">
          <h2 className="text-3xl lg:text-4xl font-bold mb-4">
            Ready to See Clearly?
          </h2>
          <p className="text-xl text-primary-100 mb-8 max-w-2xl mx-auto">
            Upload your prescription and find your perfect pair of glasses today. 
            Free shipping on orders over $100.
          </p>
          <Link to="/products?category=prescription" className="btn bg-white text-primary-600 hover:bg-gray-100 btn-lg">
            Get Started
            <ArrowRight className="ml-2 h-5 w-5" />
          </Link>
        </div>
      </section>
    </div>
  );
};

export default HomePage;
