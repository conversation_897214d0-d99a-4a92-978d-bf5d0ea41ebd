const mongoose = require('mongoose');

const productSchema = new mongoose.Schema({
  name: {
    type: String,
    required: [true, 'Product name is required'],
    trim: true,
    maxlength: [100, 'Product name cannot exceed 100 characters']
  },
  description: {
    type: String,
    required: [true, 'Product description is required'],
    maxlength: [2000, 'Description cannot exceed 2000 characters']
  },
  shortDescription: {
    type: String,
    maxlength: [200, 'Short description cannot exceed 200 characters']
  },
  price: {
    type: Number,
    required: [true, 'Price is required'],
    min: [0, 'Price cannot be negative']
  },
  comparePrice: {
    type: Number,
    min: [0, 'Compare price cannot be negative']
  },
  sku: {
    type: String,
    required: [true, 'SKU is required'],
    unique: true,
    trim: true,
    uppercase: true
  },
  category: {
    type: String,
    required: [true, 'Category is required'],
    enum: ['prescription', 'sunglasses', 'reading', 'computer', 'safety']
  },
  frameStyle: {
    type: String,
    required: [true, 'Frame style is required'],
    enum: ['aviator', 'wayfarer', 'round', 'square', 'cat-eye', 'rectangular', 'oval', 'rimless']
  },
  material: {
    type: String,
    required: [true, 'Material is required'],
    enum: ['acetate', 'metal', 'titanium', 'plastic', 'wood', 'carbon-fiber']
  },
  color: {
    type: String,
    required: [true, 'Color is required'],
    trim: true
  },
  size: {
    lensWidth: { type: Number, required: true },
    bridgeWidth: { type: Number, required: true },
    templeLength: { type: Number, required: true }
  },
  gender: {
    type: String,
    enum: ['men', 'women', 'unisex'],
    default: 'unisex'
  },
  images: [{
    url: { type: String, required: true },
    alt: { type: String, default: '' },
    isPrimary: { type: Boolean, default: false }
  }],
  features: [String],
  lensOptions: {
    prescriptionAvailable: { type: Boolean, default: true },
    blueLight: { type: Boolean, default: false },
    antiReflective: { type: Boolean, default: false },
    photochromic: { type: Boolean, default: false },
    polarized: { type: Boolean, default: false }
  },
  inventory: {
    quantity: { type: Number, required: true, min: 0 },
    lowStockThreshold: { type: Number, default: 10 },
    trackQuantity: { type: Boolean, default: true }
  },
  seo: {
    metaTitle: String,
    metaDescription: String,
    slug: { type: String, unique: true }
  },
  ratings: {
    average: { type: Number, default: 0, min: 0, max: 5 },
    count: { type: Number, default: 0 }
  },
  isActive: { type: Boolean, default: true },
  isFeatured: { type: Boolean, default: false },
  tags: [String],
  weight: Number, // in grams
  dimensions: {
    length: Number,
    width: Number,
    height: Number
  }
}, {
  timestamps: true
});

// Indexes for better query performance
productSchema.index({ category: 1, isActive: 1 });
productSchema.index({ frameStyle: 1, isActive: 1 });
productSchema.index({ material: 1, isActive: 1 });
productSchema.index({ price: 1 });
productSchema.index({ 'ratings.average': -1 });
productSchema.index({ isFeatured: 1, isActive: 1 });
productSchema.index({ sku: 1 });
productSchema.index({ 'seo.slug': 1 });

// Text search index
productSchema.index({
  name: 'text',
  description: 'text',
  shortDescription: 'text',
  tags: 'text'
});

// Virtual for discount percentage
productSchema.virtual('discountPercentage').get(function() {
  if (this.comparePrice && this.comparePrice > this.price) {
    return Math.round(((this.comparePrice - this.price) / this.comparePrice) * 100);
  }
  return 0;
});

// Virtual for stock status
productSchema.virtual('stockStatus').get(function() {
  if (!this.inventory.trackQuantity) return 'in-stock';
  if (this.inventory.quantity === 0) return 'out-of-stock';
  if (this.inventory.quantity <= this.inventory.lowStockThreshold) return 'low-stock';
  return 'in-stock';
});

// Generate slug before saving
productSchema.pre('save', function(next) {
  if (this.isModified('name') && !this.seo.slug) {
    this.seo.slug = this.name
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, '-')
      .replace(/(^-|-$)/g, '');
  }
  next();
});

// Ensure only one primary image
productSchema.pre('save', function(next) {
  const primaryImages = this.images.filter(img => img.isPrimary);
  if (primaryImages.length > 1) {
    // Keep only the first primary image
    this.images.forEach((img, index) => {
      if (img.isPrimary && index > 0) {
        img.isPrimary = false;
      }
    });
  } else if (primaryImages.length === 0 && this.images.length > 0) {
    // Set first image as primary if none is set
    this.images[0].isPrimary = true;
  }
  next();
});

module.exports = mongoose.model('Product', productSchema);
