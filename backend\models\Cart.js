const mongoose = require('mongoose');

const cartItemSchema = new mongoose.Schema({
  product: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Product',
    required: true
  },
  quantity: {
    type: Number,
    required: true,
    min: [1, 'Quantity must be at least 1'],
    max: [10, 'Maximum quantity per item is 10']
  },
  prescriptionFile: {
    filename: String,
    originalName: String,
    mimetype: String,
    size: Number,
    uploadDate: { type: Date, default: Date.now }
  },
  lensOptions: {
    blueLight: { type: Boolean, default: false },
    antiReflective: { type: Boolean, default: false },
    photochromic: { type: Boolean, default: false },
    polarized: { type: Boolean, default: false }
  },
  customizations: {
    engraving: String,
    specialInstructions: String
  },
  priceAtTime: {
    type: Number,
    required: true
  }
}, {
  timestamps: true
});

const cartSchema = new mongoose.Schema({
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    unique: true
  },
  items: [cartItemSchema],
  totalItems: {
    type: Number,
    default: 0
  },
  subtotal: {
    type: Number,
    default: 0
  },
  estimatedTax: {
    type: Number,
    default: 0
  },
  estimatedShipping: {
    type: Number,
    default: 0
  },
  estimatedTotal: {
    type: Number,
    default: 0
  },
  lastUpdated: {
    type: Date,
    default: Date.now
  },
  expiresAt: {
    type: Date,
    default: Date.now,
    expires: 2592000 // 30 days in seconds
  }
}, {
  timestamps: true
});

// Index for better query performance
cartSchema.index({ user: 1 });
cartSchema.index({ expiresAt: 1 }, { expireAfterSeconds: 0 });

// Calculate totals before saving
cartSchema.pre('save', function(next) {
  this.totalItems = this.items.reduce((total, item) => total + item.quantity, 0);
  this.subtotal = this.items.reduce((total, item) => total + (item.priceAtTime * item.quantity), 0);
  
  // Calculate estimated tax (8.5% for demo purposes)
  this.estimatedTax = Math.round(this.subtotal * 0.085 * 100) / 100;
  
  // Calculate estimated shipping (free over $100, otherwise $9.99)
  this.estimatedShipping = this.subtotal >= 100 ? 0 : 9.99;
  
  // Calculate estimated total
  this.estimatedTotal = this.subtotal + this.estimatedTax + this.estimatedShipping;
  
  this.lastUpdated = new Date();
  next();
});

// Method to add item to cart
cartSchema.methods.addItem = function(productId, quantity, priceAtTime, options = {}) {
  const existingItemIndex = this.items.findIndex(
    item => item.product.toString() === productId.toString()
  );
  
  if (existingItemIndex > -1) {
    // Update existing item
    this.items[existingItemIndex].quantity += quantity;
    this.items[existingItemIndex].priceAtTime = priceAtTime;
    
    // Update options if provided
    if (options.lensOptions) {
      this.items[existingItemIndex].lensOptions = {
        ...this.items[existingItemIndex].lensOptions,
        ...options.lensOptions
      };
    }
    
    if (options.customizations) {
      this.items[existingItemIndex].customizations = {
        ...this.items[existingItemIndex].customizations,
        ...options.customizations
      };
    }
    
    if (options.prescriptionFile) {
      this.items[existingItemIndex].prescriptionFile = options.prescriptionFile;
    }
  } else {
    // Add new item
    this.items.push({
      product: productId,
      quantity,
      priceAtTime,
      lensOptions: options.lensOptions || {},
      customizations: options.customizations || {},
      prescriptionFile: options.prescriptionFile || null
    });
  }
  
  return this.save();
};

// Method to remove item from cart
cartSchema.methods.removeItem = function(productId) {
  this.items = this.items.filter(
    item => item.product.toString() !== productId.toString()
  );
  return this.save();
};

// Method to update item quantity
cartSchema.methods.updateItemQuantity = function(productId, quantity) {
  const item = this.items.find(
    item => item.product.toString() === productId.toString()
  );
  
  if (item) {
    if (quantity <= 0) {
      return this.removeItem(productId);
    } else {
      item.quantity = quantity;
      return this.save();
    }
  }
  
  throw new Error('Item not found in cart');
};

// Method to clear cart
cartSchema.methods.clearCart = function() {
  this.items = [];
  return this.save();
};

module.exports = mongoose.model('Cart', cartSchema);
