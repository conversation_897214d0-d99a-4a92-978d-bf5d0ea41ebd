@echo off
echo ========================================
echo Starting IVIO Glasses Marketplace
echo ========================================
echo.

REM Check if .env files exist
if not exist "backend\.env" (
    echo ERROR: backend/.env file not found!
    echo Please run setup-windows.bat first or create the .env file manually.
    pause
    exit /b 1
)

if not exist "frontend\.env" (
    echo ERROR: frontend/.env file not found!
    echo Please run setup-windows.bat first or create the .env file manually.
    pause
    exit /b 1
)

echo Starting Backend Server...
start "IVIO Backend" cmd /k "cd backend && npm run dev:windows"

echo Waiting 3 seconds for backend to start...
timeout /t 3 /nobreak >nul

echo Starting Frontend Server...
start "IVIO Frontend" cmd /k "cd frontend && npm start"

echo.
echo ========================================
echo Servers Starting...
echo ========================================
echo.
echo Backend will be available at: http://localhost:5000
echo Frontend will be available at: http://localhost:3000
echo.
echo Press any key to close this window...
pause >nul
