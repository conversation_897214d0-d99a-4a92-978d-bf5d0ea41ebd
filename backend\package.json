{"name": "ivio-glasses-backend", "version": "1.0.0", "description": "Backend API for IVIO Prescription Glasses Marketplace", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "dev:windows": "set NODE_ENV=development && nodemon server.js", "test": "jest", "install-deps": "npm install"}, "keywords": ["glasses", "prescription", "marketplace", "ecommerce"], "author": "IVIO Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "mongoose": "^7.5.0", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "cors": "^2.8.5", "dotenv": "^16.3.1", "multer": "^1.4.5-lts.1", "stripe": "^13.6.0", "nodemailer": "^6.9.4", "express-validator": "^7.0.1", "helmet": "^7.0.0", "express-rate-limit": "^6.10.0", "compression": "^1.7.4"}, "devDependencies": {"nodemon": "^3.0.1", "jest": "^29.6.4", "supertest": "^6.3.3"}, "engines": {"node": ">=16.0.0"}}