@echo off
echo ========================================
echo IVIO Glasses Marketplace Setup (Windows)
echo ========================================
echo.

REM Check if Node.js is installed
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Node.js is not installed!
    echo Please download and install Node.js from https://nodejs.org/
    echo Recommended version: 18.x or higher
    pause
    exit /b 1
)

echo Node.js version:
node --version
echo.

REM Check if npm is available
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: npm is not available!
    pause
    exit /b 1
)

echo npm version:
npm --version
echo.

echo ========================================
echo Installing Backend Dependencies...
echo ========================================
cd backend
if %errorlevel% neq 0 (
    echo ERROR: backend directory not found!
    pause
    exit /b 1
)

call npm install
if %errorlevel% neq 0 (
    echo ERROR: Failed to install backend dependencies!
    pause
    exit /b 1
)

echo Backend dependencies installed successfully!
echo.

echo ========================================
echo Installing Frontend Dependencies...
echo ========================================
cd ..\frontend
if %errorlevel% neq 0 (
    echo ERROR: frontend directory not found!
    pause
    exit /b 1
)

call npm install --legacy-peer-deps
if %errorlevel% neq 0 (
    echo ERROR: Failed to install frontend dependencies!
    pause
    exit /b 1
)

echo Frontend dependencies installed successfully!
echo.

cd ..

echo ========================================
echo Creating Environment Files...
echo ========================================

REM Create backend .env file if it doesn't exist
if not exist "backend\.env" (
    echo Creating backend/.env file...
    copy "backend\.env.example" "backend\.env" >nul 2>&1
    echo Backend .env file created from example.
    echo Please edit backend/.env with your configuration.
) else (
    echo Backend .env file already exists.
)

REM Create frontend .env file if it doesn't exist
if not exist "frontend\.env" (
    echo Creating frontend/.env file...
    copy "frontend\.env.example" "frontend\.env" >nul 2>&1
    echo Frontend .env file created from example.
    echo Please edit frontend/.env with your configuration.
) else (
    echo Frontend .env file already exists.
)

echo.
echo ========================================
echo Setup Complete!
echo ========================================
echo.
echo Next steps:
echo 1. Edit backend/.env with your MongoDB URI and other settings
echo 2. Edit frontend/.env with your API URL and Stripe keys
echo 3. Run 'start-dev.bat' to start both servers
echo.
echo For manual startup:
echo - Backend: cd backend && npm run dev:windows
echo - Frontend: cd frontend && npm start
echo.
pause
