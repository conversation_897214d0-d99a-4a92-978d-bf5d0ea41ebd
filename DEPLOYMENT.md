# IVIO Glasses - Deployment Guide

This guide provides instructions for deploying the IVIO Glasses marketplace application to various hosting platforms.

## Prerequisites

- Node.js (v16 or higher)
- MongoDB database (local or cloud)
- Stripe account for payments
- Email service (Gmail, SendGrid, etc.)

## Environment Setup

### Backend Environment Variables

Create a `.env` file in the `backend` directory:

```env
# Database
MONGODB_URI=mongodb://localhost:27017/ivio-glasses
# For MongoDB Atlas: mongodb+srv://username:<EMAIL>/ivio-glasses

# JWT
JWT_SECRET=your-super-secret-jwt-key-here-make-it-long-and-random

# Server
PORT=5000
NODE_ENV=production

# Stripe
STRIPE_SECRET_KEY=sk_live_your_stripe_secret_key_here

# Email Configuration
EMAIL_SERVICE=gmail
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-app-password

# File Upload
MAX_FILE_SIZE=5242880
UPLOAD_PATH=./uploads

# CORS
FRONTEND_URL=https://your-frontend-domain.com
```

### Frontend Environment Variables

Create a `.env` file in the `frontend` directory:

```env
# API Configuration
REACT_APP_API_URL=https://your-backend-domain.com/api

# Stripe
REACT_APP_STRIPE_PUBLISHABLE_KEY=pk_live_your_stripe_publishable_key_here
```

## Local Development

### Backend Setup

```bash
cd backend
npm install
npm run dev
```

### Frontend Setup

```bash
cd frontend
npm install
npm start
```

## Production Deployment

### Option 1: Heroku Deployment

#### Backend Deployment

1. Install Heroku CLI
2. Create a new Heroku app:
   ```bash
   cd backend
   heroku create ivio-glasses-api
   ```

3. Set environment variables:
   ```bash
   heroku config:set MONGODB_URI=your_mongodb_uri
   heroku config:set JWT_SECRET=your_jwt_secret
   heroku config:set STRIPE_SECRET_KEY=your_stripe_secret
   heroku config:set EMAIL_USER=your_email
   heroku config:set EMAIL_PASS=your_email_password
   heroku config:set NODE_ENV=production
   ```

4. Deploy:
   ```bash
   git add .
   git commit -m "Deploy to Heroku"
   git push heroku main
   ```

#### Frontend Deployment

1. Build the frontend:
   ```bash
   cd frontend
   npm run build
   ```

2. Deploy to Netlify, Vercel, or similar:
   - Upload the `build` folder
   - Set environment variables in the hosting platform

### Option 2: DigitalOcean/AWS/VPS Deployment

#### Backend Deployment

1. Set up a server (Ubuntu 20.04 recommended)
2. Install Node.js and PM2:
   ```bash
   curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
   sudo apt-get install -y nodejs
   sudo npm install -g pm2
   ```

3. Clone and setup the application:
   ```bash
   git clone your-repo-url
   cd ivio-glasses/backend
   npm install --production
   ```

4. Create ecosystem file for PM2:
   ```javascript
   // ecosystem.config.js
   module.exports = {
     apps: [{
       name: 'ivio-api',
       script: 'server.js',
       instances: 'max',
       exec_mode: 'cluster',
       env: {
         NODE_ENV: 'production',
         PORT: 5000
       }
     }]
   };
   ```

5. Start with PM2:
   ```bash
   pm2 start ecosystem.config.js
   pm2 startup
   pm2 save
   ```

6. Set up Nginx reverse proxy:
   ```nginx
   server {
       listen 80;
       server_name your-api-domain.com;

       location / {
           proxy_pass http://localhost:5000;
           proxy_http_version 1.1;
           proxy_set_header Upgrade $http_upgrade;
           proxy_set_header Connection 'upgrade';
           proxy_set_header Host $host;
           proxy_set_header X-Real-IP $remote_addr;
           proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
           proxy_set_header X-Forwarded-Proto $scheme;
           proxy_cache_bypass $http_upgrade;
       }
   }
   ```

#### Frontend Deployment

1. Build the frontend:
   ```bash
   cd frontend
   npm run build
   ```

2. Serve with Nginx:
   ```nginx
   server {
       listen 80;
       server_name your-domain.com;
       root /path/to/build;
       index index.html;

       location / {
           try_files $uri $uri/ /index.html;
       }

       location /static/ {
           expires 1y;
           add_header Cache-Control "public, immutable";
       }
   }
   ```

### Option 3: Docker Deployment

#### Backend Dockerfile

```dockerfile
FROM node:18-alpine

WORKDIR /app

COPY package*.json ./
RUN npm ci --only=production

COPY . .

EXPOSE 5000

CMD ["npm", "start"]
```

#### Frontend Dockerfile

```dockerfile
FROM node:18-alpine as build

WORKDIR /app
COPY package*.json ./
RUN npm ci

COPY . .
RUN npm run build

FROM nginx:alpine
COPY --from=build /app/build /usr/share/nginx/html
COPY nginx.conf /etc/nginx/nginx.conf

EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
```

#### Docker Compose

```yaml
version: '3.8'

services:
  mongodb:
    image: mongo:5
    restart: always
    volumes:
      - mongodb_data:/data/db
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: password

  backend:
    build: ./backend
    restart: always
    ports:
      - "5000:5000"
    environment:
      - MONGODB_URI=********************************************************************
      - JWT_SECRET=your-jwt-secret
      - STRIPE_SECRET_KEY=your-stripe-secret
    depends_on:
      - mongodb

  frontend:
    build: ./frontend
    restart: always
    ports:
      - "80:80"
    depends_on:
      - backend

volumes:
  mongodb_data:
```

## Database Setup

### MongoDB Atlas (Recommended for production)

1. Create a MongoDB Atlas account
2. Create a new cluster
3. Create a database user
4. Whitelist your server IP addresses
5. Get the connection string and update MONGODB_URI

### Local MongoDB

```bash
# Install MongoDB
sudo apt-get install mongodb

# Start MongoDB
sudo systemctl start mongodb
sudo systemctl enable mongodb
```

## SSL Certificate Setup

### Using Let's Encrypt (Certbot)

```bash
sudo apt install certbot python3-certbot-nginx
sudo certbot --nginx -d your-domain.com -d api.your-domain.com
```

## Monitoring and Logging

### PM2 Monitoring

```bash
pm2 monit
pm2 logs
pm2 restart all
```

### Log Rotation

```bash
pm2 install pm2-logrotate
```

## Backup Strategy

### Database Backup

```bash
# Create backup script
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
mongodump --uri="your-mongodb-uri" --out="/backups/mongodb_$DATE"
```

### File Backup

```bash
# Backup uploads directory
tar -czf uploads_backup_$(date +%Y%m%d).tar.gz uploads/
```

## Performance Optimization

### Backend Optimizations

1. Enable gzip compression
2. Use Redis for session storage
3. Implement rate limiting
4. Use CDN for static assets

### Frontend Optimizations

1. Enable service worker for caching
2. Optimize images
3. Use lazy loading
4. Implement code splitting

## Security Checklist

- [ ] Use HTTPS in production
- [ ] Set secure environment variables
- [ ] Enable CORS properly
- [ ] Use helmet.js for security headers
- [ ] Implement rate limiting
- [ ] Validate all inputs
- [ ] Use secure session configuration
- [ ] Regular security updates

## Troubleshooting

### Common Issues

1. **CORS errors**: Check FRONTEND_URL in backend .env
2. **Database connection**: Verify MONGODB_URI
3. **File upload issues**: Check upload directory permissions
4. **Email not sending**: Verify email service configuration

### Health Checks

- Backend: `GET /api/health`
- Database: Check MongoDB connection
- File uploads: Test prescription upload
- Payments: Test Stripe integration

## Support

For deployment issues, check:
1. Server logs
2. Application logs
3. Database logs
4. Network connectivity
5. Environment variables
