import React, { createContext, useContext, useReducer, useEffect } from 'react';
import { cartAPI, handleApiError } from '../utils/api';
import { useAuth } from './AuthContext';
import toast from 'react-hot-toast';

const CartContext = createContext();

// Cart reducer
const cartReducer = (state, action) => {
  switch (action.type) {
    case 'SET_LOADING':
      return { ...state, loading: action.payload };
    case 'SET_CART':
      return {
        ...state,
        cart: action.payload,
        loading: false,
      };
    case 'SET_COUNT':
      return {
        ...state,
        count: action.payload,
      };
    case 'CLEAR_CART':
      return {
        ...state,
        cart: null,
        count: 0,
        loading: false,
      };
    case 'SET_ERROR':
      return {
        ...state,
        error: action.payload,
        loading: false,
      };
    case 'CLEAR_ERROR':
      return {
        ...state,
        error: null,
      };
    default:
      return state;
  }
};

const initialState = {
  cart: null,
  count: 0,
  loading: false,
  error: null,
};

export const CartProvider = ({ children }) => {
  const [state, dispatch] = useReducer(cartReducer, initialState);
  const { isAuthenticated, user } = useAuth();

  // Load cart when user is authenticated
  useEffect(() => {
    if (isAuthenticated && user) {
      loadCart();
      loadCartCount();
    } else {
      dispatch({ type: 'CLEAR_CART' });
    }
  }, [isAuthenticated, user]);

  // Load cart function
  const loadCart = async () => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });
      const response = await cartAPI.getCart();
      dispatch({ type: 'SET_CART', payload: response.data.data.cart });
    } catch (error) {
      console.error('Failed to load cart:', error);
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  };

  // Load cart count function
  const loadCartCount = async () => {
    try {
      const response = await cartAPI.getCartCount();
      dispatch({ type: 'SET_COUNT', payload: response.data.data.count });
    } catch (error) {
      console.error('Failed to load cart count:', error);
    }
  };

  // Add to cart function
  const addToCart = async (item) => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });
      dispatch({ type: 'CLEAR_ERROR' });

      const response = await cartAPI.addToCart(item);
      dispatch({ type: 'SET_CART', payload: response.data.data.cart });
      
      // Update count
      const countResponse = await cartAPI.getCartCount();
      dispatch({ type: 'SET_COUNT', payload: countResponse.data.data.count });

      toast.success('Item added to cart!');
      return { success: true };
    } catch (error) {
      const message = error.response?.data?.message || 'Failed to add item to cart';
      dispatch({ type: 'SET_ERROR', payload: message });
      handleApiError(error);
      return { success: false, error: message };
    }
  };

  // Update cart item function
  const updateCartItem = async (productId, quantity) => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });
      dispatch({ type: 'CLEAR_ERROR' });

      const response = await cartAPI.updateCartItem(productId, quantity);
      dispatch({ type: 'SET_CART', payload: response.data.data.cart });
      
      // Update count
      const countResponse = await cartAPI.getCartCount();
      dispatch({ type: 'SET_COUNT', payload: countResponse.data.data.count });

      if (quantity === 0) {
        toast.success('Item removed from cart');
      } else {
        toast.success('Cart updated');
      }
      return { success: true };
    } catch (error) {
      const message = error.response?.data?.message || 'Failed to update cart';
      dispatch({ type: 'SET_ERROR', payload: message });
      handleApiError(error);
      return { success: false, error: message };
    }
  };

  // Remove from cart function
  const removeFromCart = async (productId) => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });
      dispatch({ type: 'CLEAR_ERROR' });

      const response = await cartAPI.removeFromCart(productId);
      dispatch({ type: 'SET_CART', payload: response.data.data.cart });
      
      // Update count
      const countResponse = await cartAPI.getCartCount();
      dispatch({ type: 'SET_COUNT', payload: countResponse.data.data.count });

      toast.success('Item removed from cart');
      return { success: true };
    } catch (error) {
      const message = error.response?.data?.message || 'Failed to remove item from cart';
      dispatch({ type: 'SET_ERROR', payload: message });
      handleApiError(error);
      return { success: false, error: message };
    }
  };

  // Clear cart function
  const clearCart = async () => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });
      dispatch({ type: 'CLEAR_ERROR' });

      const response = await cartAPI.clearCart();
      dispatch({ type: 'SET_CART', payload: response.data.data.cart });
      dispatch({ type: 'SET_COUNT', payload: 0 });

      toast.success('Cart cleared');
      return { success: true };
    } catch (error) {
      const message = error.response?.data?.message || 'Failed to clear cart';
      dispatch({ type: 'SET_ERROR', payload: message });
      handleApiError(error);
      return { success: false, error: message };
    }
  };

  // Get cart total
  const getCartTotal = () => {
    if (!state.cart || !state.cart.items) return 0;
    return state.cart.items.reduce((total, item) => {
      return total + (item.priceAtTime * item.quantity);
    }, 0);
  };

  // Get cart item count
  const getCartItemCount = () => {
    if (!state.cart || !state.cart.items) return 0;
    return state.cart.items.reduce((total, item) => total + item.quantity, 0);
  };

  // Check if product is in cart
  const isInCart = (productId) => {
    if (!state.cart || !state.cart.items) return false;
    return state.cart.items.some(item => item.product._id === productId);
  };

  // Get cart item by product ID
  const getCartItem = (productId) => {
    if (!state.cart || !state.cart.items) return null;
    return state.cart.items.find(item => item.product._id === productId);
  };

  const value = {
    ...state,
    addToCart,
    updateCartItem,
    removeFromCart,
    clearCart,
    loadCart,
    loadCartCount,
    getCartTotal,
    getCartItemCount,
    isInCart,
    getCartItem,
  };

  return (
    <CartContext.Provider value={value}>
      {children}
    </CartContext.Provider>
  );
};

export const useCart = () => {
  const context = useContext(CartContext);
  if (!context) {
    throw new Error('useCart must be used within a CartProvider');
  }
  return context;
};
