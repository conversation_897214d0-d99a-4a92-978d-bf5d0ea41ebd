# 🪟 Windows Setup Guide for IVIO Glasses Marketplace

This guide provides step-by-step instructions to set up and run the IVIO Glasses marketplace on Windows.

## 📋 Prerequisites

### 1. Install Node.js
1. Go to [nodejs.org](https://nodejs.org/)
2. Download the **LTS version** (18.x or higher)
3. Run the installer and follow the setup wizard
4. **Important**: Check "Add to PATH" during installation
5. Verify installation:
   ```cmd
   node --version
   npm --version
   ```

### 2. Install MongoDB (Choose One Option)

#### Option A: MongoDB Atlas (Recommended - Cloud Database)
1. Go to [MongoDB Atlas](https://www.mongodb.com/atlas)
2. Create a free account
3. Create a new cluster (free tier available)
4. Create a database user
5. Get your connection string
6. Whitelist your IP address

#### Option B: Local MongoDB Installation
1. Go to [MongoDB Download Center](https://www.mongodb.com/try/download/community)
2. Download MongoDB Community Server for Windows
3. Run the installer
4. Choose "Complete" installation
5. Install as a Windows Service
6. Install MongoDB Compass (GUI tool)

### 3. Get Stripe Account
1. Go to [Stripe](https://stripe.com/)
2. Create an account
3. Get your API keys from Dashboard > Developers > API keys
4. Use **test keys** for development

### 4. Setup Email Service (Gmail Recommended)
1. Enable 2-factor authentication on your Gmail account
2. Go to Google Account > Security > App passwords
3. Generate an app password for "Mail"
4. Use this app password (not your regular Gmail password)

## 🚀 Quick Setup (Automated)

### Step 1: Download and Extract
1. Download the project files
2. Extract to a folder like `C:\ivio-glasses`
3. Open Command Prompt as Administrator

### Step 2: Run Automated Setup
```cmd
cd C:\ivio-glasses
setup-windows.bat
```

This script will:
- Check Node.js installation
- Install all dependencies
- Create environment files
- Set up the project structure

### Step 3: Configure Environment Variables

Edit `backend\.env`:
```env
MONGODB_URI=your_mongodb_connection_string
JWT_SECRET=your-super-secret-jwt-key-make-it-long-and-random
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-gmail-app-password
```

Edit `frontend\.env`:
```env
REACT_APP_STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key
```

### Step 4: Start the Application
```cmd
start-dev.bat
```

This will open two command windows:
- Backend server (http://localhost:5000)
- Frontend server (http://localhost:3000)

## 🛠️ Manual Setup (If Automated Setup Fails)

### Step 1: Install Backend Dependencies
```cmd
cd backend
npm install
```

If you get errors, try:
```cmd
npm install --force
```

### Step 2: Install Frontend Dependencies
```cmd
cd frontend
npm install --legacy-peer-deps
```

If you get "react-scripts is not recognized" error:
```cmd
npm install react-scripts --save
npm install --legacy-peer-deps
```

### Step 3: Create Environment Files
Copy the example files and edit them:
```cmd
cd backend
copy .env.example .env
notepad .env

cd ..\frontend
copy .env.example .env
notepad .env
```

### Step 4: Start Servers Manually
Open two separate Command Prompt windows:

**Terminal 1 - Backend:**
```cmd
cd backend
npm run dev:windows
```

**Terminal 2 - Frontend:**
```cmd
cd frontend
npm start
```

## 🔧 Troubleshooting Common Windows Issues

### Issue: "react-scripts is not recognized"
**Solution:**
```cmd
cd frontend
npm install react-scripts --save-dev
npm install --legacy-peer-deps
```

### Issue: "Permission denied" errors
**Solution:**
- Run Command Prompt as Administrator
- Or use PowerShell as Administrator

### Issue: Port already in use
**Solution:**
```cmd
# Kill processes on ports 3000 and 5000
netstat -ano | findstr :3000
netstat -ano | findstr :5000
taskkill /PID <process_id> /F
```

### Issue: MongoDB connection failed
**Solutions:**
- Check if MongoDB service is running: `services.msc`
- Verify connection string in `.env`
- For Atlas: Check IP whitelist and credentials

### Issue: Stripe payments not working
**Solutions:**
- Verify you're using test keys
- Check both backend and frontend `.env` files
- Ensure keys match (secret key in backend, publishable key in frontend)

### Issue: Email not sending
**Solutions:**
- Use Gmail App Password, not regular password
- Enable 2-factor authentication first
- Check if "Less secure app access" is enabled (if not using app password)

### Issue: File upload errors
**Solutions:**
- Check if `uploads` folder exists in backend
- Verify file permissions
- Check `MAX_FILE_SIZE` in `.env`

## 📁 Windows-Specific File Paths

The application uses these paths on Windows:
- **Backend uploads**: `backend\uploads\`
- **Environment files**: `backend\.env` and `frontend\.env`
- **Log files**: `backend\logs\` (if logging is enabled)

## 🔒 Windows Security Considerations

### Firewall Settings
If you have issues accessing the application:
1. Open Windows Defender Firewall
2. Allow Node.js through firewall
3. Or temporarily disable firewall for testing

### Antivirus Software
Some antivirus software may block Node.js:
1. Add Node.js to antivirus exceptions
2. Add the project folder to exceptions

## 🚀 Production Deployment on Windows

### Using IIS (Internet Information Services)
1. Install IIS with Node.js support
2. Install iisnode module
3. Configure web.config for Node.js
4. Deploy built React app to IIS

### Using PM2 on Windows
```cmd
npm install -g pm2
npm install -g pm2-windows-startup

# Start backend
cd backend
pm2 start server.js --name "ivio-backend"

# Setup startup
pm2-startup install
pm2 save
```

## 📞 Getting Help

If you encounter issues:

1. **Check the logs**: Look for error messages in the command prompt windows
2. **Verify prerequisites**: Ensure Node.js, MongoDB, and other tools are properly installed
3. **Check environment variables**: Make sure all required variables are set
4. **Try manual setup**: If automated setup fails, follow manual steps
5. **Contact support**: Email <EMAIL> with error details

## 📝 Development Tips for Windows

### Recommended Tools
- **VS Code**: Best editor for this project
- **Git for Windows**: Version control
- **MongoDB Compass**: Database GUI
- **Postman**: API testing
- **Windows Terminal**: Better command line experience

### VS Code Extensions
- ES7+ React/Redux/React-Native snippets
- Tailwind CSS IntelliSense
- MongoDB for VS Code
- Thunder Client (API testing)

### Useful Commands
```cmd
# Check what's running on ports
netstat -ano | findstr :3000
netstat -ano | findstr :5000

# Clear npm cache
npm cache clean --force

# Restart MongoDB service
net stop MongoDB
net start MongoDB

# Check Node.js and npm versions
node --version
npm --version
```

---

**Happy coding! 🚀**
